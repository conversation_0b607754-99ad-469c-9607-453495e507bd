"""
Working Gesture Logger - A simplified version that is guaranteed to work
This script provides a simple GUI for selecting gestures
"""

import tkinter as tk
from tkinter import ttk
import json
from datetime import datetime

# Constants for gesture types
PEACE_SIGN = "peace_sign"  # ✌️ - logs 200ml
THUMBS_UP = "thumbs_up"    # 👍 - logs 330ml

def main():
    """Main function"""
    # Create the main window
    root = tk.Tk()
    root.title("Gesture Logger")
    root.geometry("400x300")
    
    # Create a frame for the content
    frame = ttk.Frame(root, padding=20)
    frame.pack(fill=tk.BOTH, expand=True)
    
    # Add a title
    title = ttk.Label(frame, text="Select a Gesture", font=("Arial", 16, "bold"))
    title.pack(pady=10)
    
    # Create a variable to store the selected gesture
    selected_gesture = tk.StringVar()
    
    # Create the radio buttons
    peace_sign_radio = ttk.Radiobutton(
        frame,
        text="Peace Sign ✌️ (200ml)",
        variable=selected_gesture,
        value=PEACE_SIGN
    )
    peace_sign_radio.pack(pady=5, anchor=tk.W)
    
    thumbs_up_radio = ttk.Radiobutton(
        frame,
        text="Thumbs Up 👍 (330ml)",
        variable=selected_gesture,
        value=THUMBS_UP
    )
    thumbs_up_radio.pack(pady=5, anchor=tk.W)
    
    # Create a status label
    status_label = ttk.Label(frame, text="")
    status_label.pack(pady=10)
    
    # Function to handle the submit button
    def submit():
        gesture = selected_gesture.get()
        if not gesture:
            status_label.config(text="Please select a gesture", foreground="red")
            return
        
        # Create the result data
        amount = 200 if gesture == PEACE_SIGN else 330
        result = {
            "success": True,
            "gesture": gesture,
            "timestamp": datetime.now().isoformat(),
            "amount": amount
        }
        
        # Save the result to a file
        with open("gesture_result.json", "w") as f:
            json.dump(result, f)
        
        # Print the result for the subprocess to capture
        print(f"GESTURE_DETECTED:{gesture}")
        
        # Close the window
        root.destroy()
    
    # Function to handle the cancel button
    def cancel():
        # Create the result data
        result = {
            "success": False,
            "gesture": None,
            "timestamp": datetime.now().isoformat()
        }
        
        # Save the result to a file
        with open("gesture_result.json", "w") as f:
            json.dump(result, f)
        
        # Print the result for the subprocess to capture
        print("NO_GESTURE_DETECTED")
        
        # Close the window
        root.destroy()
    
    # Create the buttons
    button_frame = ttk.Frame(frame)
    button_frame.pack(pady=20)
    
    submit_button = ttk.Button(button_frame, text="Submit", command=submit)
    submit_button.grid(row=0, column=0, padx=10)
    
    cancel_button = ttk.Button(button_frame, text="Cancel", command=cancel)
    cancel_button.grid(row=0, column=1, padx=10)
    
    # Start the main loop
    root.mainloop()

if __name__ == "__main__":
    main()
