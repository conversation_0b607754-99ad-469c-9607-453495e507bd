{% extends 'base.html' %}

{% block title %}Export Data - HydroMate{% endblock %}

{% block extra_css %}
<style>
    .export-card {
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .format-option {
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 10px;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .format-option:hover {
        border-color: #007bff;
        background-color: #f8f9fa;
    }

    .format-option.selected {
        border-color: #007bff;
        background-color: #e3f2fd;
    }

    .format-icon {
        font-size: 2rem;
        margin-bottom: 10px;
    }

    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }

    .stats-item {
        text-align: center;
        margin-bottom: 15px;
    }

    .stats-number {
        font-size: 2rem;
        font-weight: bold;
        display: block;
    }

    .stats-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }

    .date-input {
        margin-bottom: 15px;
    }

    .drink-type-checkbox {
        margin-bottom: 8px;
    }

    .loading-spinner {
        display: none;
        text-align: center;
        padding: 20px;
    }

    .preview-section {
        display: none;
        margin-top: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <h2 class="mb-4">
        <i class="bi bi-download"></i> Export Your Data
    </h2>
    <p class="text-muted mb-4">Export your water intake data in various formats for analysis, sharing, or backup purposes.</p>

    <div class="row">
        <div class="col-md-8">
            <!-- Export Configuration -->
            <div class="card export-card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-gear"></i> Export Configuration</h5>
                </div>
                <div class="card-body">
                    <form id="exportForm">
                        <!-- Format Selection -->
                        <div class="mb-4">
                            <label class="form-label"><strong>Export Format</strong></label>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="format-option" data-format="csv">
                                        <div class="text-center">
                                            <div class="format-icon text-success">
                                                <i class="bi bi-file-earmark-spreadsheet"></i>
                                            </div>
                                            <h6>CSV</h6>
                                            <small class="text-muted">Comma-separated values for spreadsheet analysis</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="format-option" data-format="json">
                                        <div class="text-center">
                                            <div class="format-icon text-info">
                                                <i class="bi bi-file-earmark-code"></i>
                                            </div>
                                            <h6>JSON</h6>
                                            <small class="text-muted">Structured data format for developers</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="format-option" data-format="pdf">
                                        <div class="text-center">
                                            <div class="format-icon text-danger">
                                                <i class="bi bi-file-earmark-pdf"></i>
                                            </div>
                                            <h6>PDF Report</h6>
                                            <small class="text-muted">Professional report with charts</small>
                                            <div class="mt-2">
                                                <small class="text-warning">
                                                    <i class="bi bi-info-circle"></i>
                                                    Note: Will export as text file if PDF libraries not installed
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <input type="hidden" id="selectedFormat" name="format" value="csv">
                        </div>

                        <!-- Date Range -->
                        <div class="mb-4">
                            <label class="form-label"><strong>Date Range</strong></label>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="date-input">
                                        <label for="startDate" class="form-label">Start Date</label>
                                        <input type="date" class="form-control" id="startDate" name="start_date">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="date-input">
                                        <label for="endDate" class="form-label">End Date</label>
                                        <input type="date" class="form-control" id="endDate" name="end_date">
                                    </div>
                                </div>
                            </div>
                            <small class="text-muted">Leave empty to export all data</small>
                        </div>

                        <!-- Drink Types Filter -->
                        <div class="mb-4">
                            <label class="form-label"><strong>Drink Types</strong></label>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="drink-type-checkbox">
                                        <input type="checkbox" class="form-check-input" id="selectAllDrinks" checked>
                                        <label class="form-check-label" for="selectAllDrinks">
                                            <strong>Select All</strong>
                                        </label>
                                    </div>
                                    <div id="drinkTypesList">
                                        <!-- Drink types will be loaded here -->
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- PDF Options -->
                        <div class="mb-4" id="pdfOptions" style="display: none;">
                            <label class="form-label"><strong>PDF Options</strong></label>
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="includeCharts" name="include_charts" checked>
                                <label class="form-check-label" for="includeCharts">
                                    Include charts and graphs
                                </label>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-outline-primary" id="previewBtn">
                                <i class="bi bi-eye"></i> Preview
                            </button>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-download"></i> Export Data
                            </button>
                        </div>
                    </form>

                    <!-- Loading Spinner -->
                    <div class="loading-spinner" id="loadingSpinner">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Generating export...</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- Statistics Preview -->
            <div class="stats-card" id="statsCard">
                <h6 class="mb-3"><i class="bi bi-bar-chart"></i> Export Preview</h6>
                <div class="row">
                    <div class="col-6">
                        <div class="stats-item">
                            <span class="stats-number" id="totalRecords">-</span>
                            <span class="stats-label">Records</span>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="stats-item">
                            <span class="stats-number" id="totalVolume">-</span>
                            <span class="stats-label">Total (ml)</span>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="stats-item">
                            <span class="stats-number" id="totalDays">-</span>
                            <span class="stats-label">Days</span>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="stats-item">
                            <span class="stats-number" id="dailyAverage">-</span>
                            <span class="stats-label">Daily Avg</span>
                        </div>
                    </div>
                </div>
                <small class="text-light">Click "Preview" to see statistics for your selected criteria</small>
            </div>

            <!-- Quick Export Options -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="bi bi-lightning"></i> Quick Export</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary btn-sm" onclick="quickExport('last7days')">
                            Last 7 Days (CSV)
                        </button>
                        <button class="btn btn-outline-primary btn-sm" onclick="quickExport('last30days')">
                            Last 30 Days (CSV)
                        </button>
                        <button class="btn btn-outline-primary btn-sm" onclick="quickExport('thismonth')">
                            This Month (PDF)
                        </button>
                        <button class="btn btn-outline-primary btn-sm" onclick="quickExport('all')">
                            All Data (JSON)
                        </button>
                    </div>
                </div>
            </div>

            <!-- PDF Libraries Info -->
            <div class="card mt-3">
                <div class="card-header">
                    <h6 class="mb-0"><i class="bi bi-info-circle"></i> PDF Export Info</h6>
                </div>
                <div class="card-body">
                    <small class="text-muted">
                        <strong>For proper PDF reports with charts:</strong><br>
                        Install the required libraries:<br>
                        <code>pip install reportlab matplotlib pandas</code><br><br>
                        Without these libraries, PDF exports will be saved as text files.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Load drink types
    loadDrinkTypes();

    // Format selection
    document.querySelectorAll('.format-option').forEach(option => {
        option.addEventListener('click', function() {
            // Remove selected class from all options
            document.querySelectorAll('.format-option').forEach(opt => opt.classList.remove('selected'));
            // Add selected class to clicked option
            this.classList.add('selected');
            // Update hidden input
            document.getElementById('selectedFormat').value = this.dataset.format;

            // Show/hide PDF options
            const pdfOptions = document.getElementById('pdfOptions');
            if (this.dataset.format === 'pdf') {
                pdfOptions.style.display = 'block';
            } else {
                pdfOptions.style.display = 'none';
            }
        });
    });

    // Select first format by default
    document.querySelector('.format-option').click();

    // Select all drinks checkbox
    document.getElementById('selectAllDrinks').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('#drinkTypesList input[type="checkbox"]');
        checkboxes.forEach(cb => cb.checked = this.checked);
    });

    // Preview button
    document.getElementById('previewBtn').addEventListener('click', function() {
        loadPreview();
    });

    // Form submission
    document.getElementById('exportForm').addEventListener('submit', function(e) {
        e.preventDefault();
        exportData();
    });

    // Set default dates (last 30 days)
    const today = new Date();
    const thirtyDaysAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));

    document.getElementById('endDate').value = today.toISOString().split('T')[0];
    document.getElementById('startDate').value = thirtyDaysAgo.toISOString().split('T')[0];
});

function loadDrinkTypes() {
    // This would typically load from an API endpoint
    // For now, we'll use common drink types
    const drinkTypes = [
        {id: 1, name: 'Water'},
        {id: 2, name: 'Tea'},
        {id: 3, name: 'Coffee'},
        {id: 4, name: 'Milk'},
        {id: 5, name: 'Juice'},
        {id: 6, name: 'Soda'},
        {id: 7, name: 'Pepsi'}
    ];

    const container = document.getElementById('drinkTypesList');
    container.innerHTML = '';

    drinkTypes.forEach(type => {
        const div = document.createElement('div');
        div.className = 'drink-type-checkbox';
        div.innerHTML = `
            <input type="checkbox" class="form-check-input" id="drink_${type.id}" name="drink_types" value="${type.id}" checked>
            <label class="form-check-label" for="drink_${type.id}">
                ${type.name}
            </label>
        `;
        container.appendChild(div);
    });
}

function loadPreview() {
    const formData = new FormData(document.getElementById('exportForm'));

    fetch('/api/export/preview', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateStatsDisplay(data.stats);
        } else {
            alert('Error loading preview: ' + (data.error || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error loading preview');
    });
}

function updateStatsDisplay(stats) {
    document.getElementById('totalRecords').textContent = stats.total_logs || 0;
    document.getElementById('totalVolume').textContent = (stats.total_volume || 0).toLocaleString();
    document.getElementById('totalDays').textContent = stats.total_days || 0;
    document.getElementById('dailyAverage').textContent = Math.round(stats.daily_average || 0);
}

function exportData() {
    const loadingSpinner = document.getElementById('loadingSpinner');
    const form = document.getElementById('exportForm');

    loadingSpinner.style.display = 'block';

    // Create a temporary form for file download
    const tempForm = document.createElement('form');
    tempForm.method = 'POST';
    tempForm.action = '/api/export';
    tempForm.style.display = 'none';

    // Copy form data
    const formData = new FormData(form);
    for (let [key, value] of formData.entries()) {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = key;
        input.value = value;
        tempForm.appendChild(input);
    }

    document.body.appendChild(tempForm);
    tempForm.submit();
    document.body.removeChild(tempForm);

    // Hide loading spinner after a delay
    setTimeout(() => {
        loadingSpinner.style.display = 'none';
    }, 2000);
}

function quickExport(type) {
    const today = new Date();
    let startDate, endDate, format;

    switch(type) {
        case 'last7days':
            startDate = new Date(today.getTime() - (7 * 24 * 60 * 60 * 1000));
            endDate = today;
            format = 'csv';
            break;
        case 'last30days':
            startDate = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));
            endDate = today;
            format = 'csv';
            break;
        case 'thismonth':
            startDate = new Date(today.getFullYear(), today.getMonth(), 1);
            endDate = today;
            format = 'pdf';
            break;
        case 'all':
            startDate = null;
            endDate = null;
            format = 'json';
            break;
    }

    // Set form values
    document.getElementById('selectedFormat').value = format;
    document.getElementById('startDate').value = startDate ? startDate.toISOString().split('T')[0] : '';
    document.getElementById('endDate').value = endDate ? endDate.toISOString().split('T')[0] : '';

    // Update UI
    document.querySelectorAll('.format-option').forEach(opt => opt.classList.remove('selected'));
    document.querySelector(`[data-format="${format}"]`).classList.add('selected');

    // Export
    exportData();
}
</script>
{% endblock %}
