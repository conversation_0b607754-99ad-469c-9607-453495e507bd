<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
    <!-- Background Circle -->
    <circle cx="100" cy="100" r="100" fill="#4DA6FF" />
    
    <!-- Water Progress Container -->
    <rect x="130" y="40" width="30" height="120" rx="15" fill="#FFFFFF" opacity="0.3" />
    
    <!-- Water Progress (This will be animated/updated based on progress) -->
    <rect id="water-progress" x="130" y="100" width="30" height="60" rx="15" fill="#FFFFFF" />
    
    <!-- Person Silhouette -->
    <g>
        <!-- Head -->
        <circle cx="80" cy="60" r="20" fill="#FFFFFF" />
        
        <!-- Body -->
        <rect x="70" y="80" width="20" height="50" rx="10" fill="#FFFFFF" />
        
        <!-- Arms -->
        <rect x="50" y="90" width="20" height="10" rx="5" fill="#FFFFFF" />
        <rect x="90" y="90" width="20" height="10" rx="5" fill="#FFFFFF" />
        
        <!-- Legs -->
        <rect x="70" y="130" width="8" height="30" rx="4" fill="#FFFFFF" />
        <rect x="82" y="130" width="8" height="30" rx="4" fill="#FFFFFF" />
    </g>
    
    <!-- Water Drops -->
    <circle cx="110" cy="70" r="5" fill="#FFFFFF" opacity="0.7">
        <animate attributeName="cy" from="70" to="170" dur="2s" repeatCount="indefinite" />
        <animate attributeName="opacity" from="0.7" to="0" dur="2s" repeatCount="indefinite" />
    </circle>
    
    <circle cx="120" cy="50" r="4" fill="#FFFFFF" opacity="0.7">
        <animate attributeName="cy" from="50" to="170" dur="2.5s" repeatCount="indefinite" />
        <animate attributeName="opacity" from="0.7" to="0" dur="2.5s" repeatCount="indefinite" />
    </circle>
    
    <circle cx="100" cy="40" r="3" fill="#FFFFFF" opacity="0.7">
        <animate attributeName="cy" from="40" to="170" dur="3s" repeatCount="indefinite" />
        <animate attributeName="opacity" from="0.7" to="0" dur="3s" repeatCount="indefinite" />
    </circle>
    
    <!-- App Name -->
    <text x="100" y="185" font-family="Arial, sans-serif" font-size="16" font-weight="bold" text-anchor="middle" fill="#FFFFFF">HydroMate</text>
</svg>
