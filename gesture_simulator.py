"""
Gesture Simulator - A lightweight alternative to full gesture recognition
This script captures a webcam image and lets users select which gesture they made
"""

import cv2
import tkinter as tk
from tkinter import ttk, messagebox
import json
import os
import sys
import requests
from datetime import datetime
from PIL import Image, ImageTk

# Constants for gesture types
PEACE_SIGN = "peace_sign"  # ✌️ - logs 200ml
THUMBS_UP = "thumbs_up"    # 👍 - logs 330ml
NO_GESTURE = "no_gesture"  # No gesture detected

def log_water_directly(amount, gesture_type):
    """Log water directly to the app using the /log_water endpoint"""
    try:
        # Create the payload
        data = {
            'amount': amount,
            'drink_type_id': 1,  # Water
            'notes': f'Logged via gesture: {gesture_type}'
        }
        
        # Send the request to the app
        response = requests.post('http://127.0.0.1:8080/log_water', data=data)
        
        # Check if the request was successful
        if response.status_code == 200:
            return True, "Water logged successfully!"
        else:
            return False, f"Error logging water: {response.status_code}"
    except Exception as e:
        return False, f"Error logging water: {str(e)}"

def capture_frame():
    """Capture a frame from the webcam"""
    # Initialize webcam
    cap = cv2.VideoCapture(0)
    
    if not cap.isOpened():
        messagebox.showerror("Error", "Could not open webcam")
        return False
    
    # Create a preview window
    cv2.namedWindow("Webcam Preview", cv2.WINDOW_NORMAL)
    cv2.resizeWindow("Webcam Preview", 640, 480)
    
    # Instructions
    print("Press SPACE to capture or ESC to cancel")
    
    while True:
        # Read frame
        ret, frame = cap.read()
        if not ret:
            messagebox.showerror("Error", "Could not read frame from webcam")
            break
        
        # Flip the frame horizontally for a more intuitive mirror view
        frame = cv2.flip(frame, 1)
        
        # Display instructions on the frame
        cv2.putText(frame, "Press SPACE to capture", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        cv2.putText(frame, "Press ESC to cancel", (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
        
        # Display the frame
        cv2.imshow("Webcam Preview", frame)
        
        # Wait for key press
        key = cv2.waitKey(1) & 0xFF
        
        # If ESC is pressed, exit
        if key == 27:  # ESC key
            break
        
        # If SPACE is pressed, capture the frame
        if key == 32:  # SPACE key
            # Save the frame
            cv2.imwrite("gesture_image.jpg", frame)
            cap.release()
            cv2.destroyAllWindows()
            return True
    
    # Release resources
    cap.release()
    cv2.destroyAllWindows()
    return False

def show_gesture_selector():
    """Show a window to select the gesture"""
    # Check if the image exists
    if not os.path.exists("gesture_image.jpg"):
        messagebox.showerror("Error", "No gesture image found")
        return False
    
    # Create the main window
    root = tk.Tk()
    root.title("Select Your Gesture")
    root.geometry("500x600")
    
    # Create a frame for the content
    frame = ttk.Frame(root, padding=20)
    frame.pack(fill=tk.BOTH, expand=True)
    
    # Add a title
    title = ttk.Label(frame, text="Select Your Gesture", font=("Arial", 16, "bold"))
    title.pack(pady=10)
    
    # Load and display the captured image
    try:
        img = Image.open("gesture_image.jpg")
        img = img.resize((400, 300), Image.LANCZOS)
        photo = ImageTk.PhotoImage(img)
        
        image_label = ttk.Label(frame, image=photo)
        image_label.image = photo  # Keep a reference to prevent garbage collection
        image_label.pack(pady=10)
    except Exception as e:
        messagebox.showerror("Error", f"Could not load image: {str(e)}")
        root.destroy()
        return False
    
    # Create a variable to store the selected gesture
    selected_gesture = tk.StringVar(value=NO_GESTURE)
    
    # Create the radio buttons
    gesture_frame = ttk.LabelFrame(frame, text="Select the gesture you made")
    gesture_frame.pack(pady=10, fill=tk.X)
    
    no_gesture_radio = ttk.Radiobutton(
        gesture_frame,
        text="No Gesture",
        variable=selected_gesture,
        value=NO_GESTURE
    )
    no_gesture_radio.pack(pady=5, anchor=tk.W)
    
    peace_sign_radio = ttk.Radiobutton(
        gesture_frame,
        text="Peace Sign ✌️ (200ml)",
        variable=selected_gesture,
        value=PEACE_SIGN
    )
    peace_sign_radio.pack(pady=5, anchor=tk.W)
    
    thumbs_up_radio = ttk.Radiobutton(
        gesture_frame,
        text="Thumbs Up 👍 (330ml)",
        variable=selected_gesture,
        value=THUMBS_UP
    )
    thumbs_up_radio.pack(pady=5, anchor=tk.W)
    
    # Create a status label
    status_label = ttk.Label(frame, text="")
    status_label.pack(pady=10)
    
    # Function to handle the submit button
    def submit():
        gesture = selected_gesture.get()
        if gesture == NO_GESTURE:
            status_label.config(text="Please select a gesture", foreground="red")
            return
        
        # Create the result data
        amount = 200 if gesture == PEACE_SIGN else 330
        result = {
            "success": True,
            "gesture": gesture,
            "timestamp": datetime.now().isoformat(),
            "amount": amount
        }
        
        # Save the result to a file
        with open("gesture_result.json", "w") as f:
            json.dump(result, f)
        
        # Print the result for the subprocess to capture
        print(f"GESTURE_DETECTED:{gesture}")
        
        # Try to log water directly
        success, message = log_water_directly(amount, gesture)
        
        if success:
            messagebox.showinfo("Success", f"Successfully logged {amount}ml of water with {gesture} gesture!")
            root.destroy()
        else:
            status_label.config(text=message, foreground="red")
    
    # Function to handle the cancel button
    def cancel():
        # Create the result data
        result = {
            "success": False,
            "gesture": None,
            "timestamp": datetime.now().isoformat()
        }
        
        # Save the result to a file
        with open("gesture_result.json", "w") as f:
            json.dump(result, f)
        
        # Print the result for the subprocess to capture
        print("NO_GESTURE_DETECTED")
        
        # Close the window
        root.destroy()
    
    # Create the buttons
    button_frame = ttk.Frame(frame)
    button_frame.pack(pady=20)
    
    submit_button = ttk.Button(button_frame, text="Log Water", command=submit)
    submit_button.grid(row=0, column=0, padx=10)
    
    cancel_button = ttk.Button(button_frame, text="Cancel", command=cancel)
    cancel_button.grid(row=0, column=1, padx=10)
    
    # Start the main loop
    root.mainloop()
    return True

def main():
    """Main function"""
    # Capture a frame from the webcam
    if capture_frame():
        # Show the gesture selector
        show_gesture_selector()
    else:
        # Create a failure result
        result = {
            "success": False,
            "gesture": None,
            "timestamp": datetime.now().isoformat()
        }
        
        # Save the result to a file
        with open("gesture_result.json", "w") as f:
            json.dump(result, f)
        
        # Print the result for the subprocess to capture
        print("NO_GESTURE_DETECTED")

if __name__ == "__main__":
    main()
