import requests
import json

def test_weather_api():
    """Test the OpenWeatherMap API directly."""
    # API key
    api_key = "51697047df57d0d77efa8d330e6fb44d"
    
    # Test cities
    cities = ["Lahore", "Karachi", "New York", "London", "Tokyo"]
    
    print("Testing OpenWeatherMap API with different cities...")
    
    for city in cities:
        # Construct the URL as recommended
        url = f"https://api.openweathermap.org/data/2.5/weather?q={city}&appid={api_key}&units=metric"
        
        print(f"\nTesting city: {city}")
        print(f"URL: {url.replace(api_key, 'API_KEY')}")
        
        try:
            # Make the request
            response = requests.get(url)
            
            # Print status code
            print(f"Status code: {response.status_code}")
            
            if response.status_code == 200:
                # Parse the response as JSON
                data = response.json()
                
                # Print temperature
                if 'main' in data and 'temp' in data['main']:
                    print(f"Temperature: {data['main']['temp']}°C")
                
                # Print weather condition
                if 'weather' in data and len(data['weather']) > 0:
                    print(f"Weather: {data['weather'][0]['description']}")
            else:
                print(f"Error response: {response.text}")
        
        except Exception as e:
            print(f"Exception: {e}")

if __name__ == "__main__":
    test_weather_api()
