{% extends 'base.html' %}

{% block title %}Gesture Logging - HydroMate{% endblock %}

{% block extra_css %}
<style>
    .camera-container {
        position: relative;
        width: 100%;
        max-width: 640px;
        margin: 0 auto;
        overflow: hidden;
        border-radius: 8px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    #video {
        width: 100%;
        height: auto;
        background-color: #f0f0f0;
        display: block;
    }

    #canvas {
        display: none;
    }

    .camera-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        background-color: rgba(0, 0, 0, 0.5);
        color: white;
        text-align: center;
        z-index: 10;
    }

    .camera-controls {
        margin-top: 15px;
        display: flex;
        gap: 10px;
        justify-content: center;
    }

    .gesture-instructions {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
    }

    .gesture-icon {
        font-size: 2rem;
        margin-bottom: 10px;
    }

    .result-container {
        display: none;
        margin-top: 20px;
        padding: 15px;
        border-radius: 8px;
        background-color: #f8f9fa;
    }

    .result-image {
        max-width: 100%;
        border-radius: 8px;
        margin-top: 10px;
    }

    .loading-spinner {
        display: none;
        margin: 20px auto;
        text-align: center;
    }

    .gesture-feedback {
        position: absolute;
        top: 10px;
        left: 10px;
        padding: 5px 10px;
        border-radius: 4px;
        font-weight: bold;
        z-index: 5;
    }

    .gesture-feedback.success {
        background-color: rgba(40, 167, 69, 0.8);
        color: white;
    }

    .gesture-feedback.error {
        background-color: rgba(220, 53, 69, 0.8);
        color: white;
    }

    .gesture-feedback.detecting {
        background-color: rgba(255, 193, 7, 0.8);
        color: black;
    }

    .gesture-examples {
        display: flex;
        gap: 20px;
        justify-content: center;
        margin-bottom: 20px;
    }

    .gesture-example {
        text-align: center;
        padding: 10px;
        border-radius: 8px;
        background-color: #f0f0f0;
        width: 120px;
    }

    .gesture-example img {
        width: 100px;
        height: 100px;
        object-fit: contain;
    }

    .gesture-example p {
        margin-top: 5px;
        font-weight: bold;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <h2 class="mb-3">Gesture Logging</h2>
    <p class="text-muted mb-4">Log your water intake using hand gestures. Make a peace sign (✌️) to log a standard amount of water.</p>

    <!-- Gesture Instructions -->
    <div class="gesture-instructions mb-4">
        <h4><i class="bi bi-info-circle"></i> How to Use</h4>
        <p>1. Allow camera access when prompted</p>
        <p>2. Make a peace sign (✌️) gesture with your hand</p>
        <p>3. Hold the gesture steady for a moment</p>
        <p>4. The system will detect your gesture and log your water intake</p>
    </div>

    <!-- Gesture Examples -->
    <div class="gesture-examples">
        <div class="gesture-example">
            <img src="{{ url_for('static', filename='images/peace_sign.png') }}" alt="Peace Sign" onerror="this.src='https://via.placeholder.com/100?text=Peace+Sign'">
            <p>Peace Sign ✌️</p>
            <small>Logs 200ml of water</small>
        </div>
        <div class="gesture-example">
            <img src="{{ url_for('static', filename='images/thumbs_up.png') }}" alt="Thumbs Up" onerror="this.src='https://via.placeholder.com/100?text=Thumbs+Up'">
            <p>Thumbs Up 👍</p>
            <small>Logs 330ml of water</small>
        </div>
    </div>

    <!-- Camera Container -->
    <div class="camera-container mb-4">
        <video id="video" autoplay playsinline></video>
        <canvas id="canvas"></canvas>
        <div id="cameraOverlay" class="camera-overlay">
            <i class="bi bi-camera-video fs-1 mb-2"></i>
            <h4>Camera Access Required</h4>
            <p>Click the button below to start the camera</p>
            <button id="startCamera" class="btn btn-primary">
                <i class="bi bi-camera-video-fill"></i> Start Camera
            </button>
        </div>
        <div id="gestureFeedback" class="gesture-feedback"></div>
    </div>

    <!-- Camera Controls -->
    <div class="camera-controls mb-4">
        <button id="captureGesture" class="btn btn-primary" disabled>
            <i class="bi bi-hand-index-thumb"></i> Capture Gesture
        </button>
        <button id="toggleCamera" class="btn btn-secondary" disabled>
            <i class="bi bi-arrow-repeat"></i> Switch Camera
        </button>
    </div>

    <!-- Loading Spinner -->
    <div id="loadingSpinner" class="loading-spinner">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mt-2">Processing gesture...</p>
    </div>

    <!-- Result Container -->
    <div id="resultContainer" class="result-container">
        <h4 id="resultTitle">Gesture Detected</h4>
        <p id="resultMessage"></p>
        <div id="resultActions" class="d-flex gap-2 mt-3">
            <button id="logWater" class="btn btn-success">
                <i class="bi bi-plus-circle"></i> Log Water
            </button>
            <button id="tryAgain" class="btn btn-secondary">
                <i class="bi bi-arrow-counterclockwise"></i> Try Again
            </button>
        </div>
        <img id="resultImage" class="result-image" src="" alt="Detected Gesture">
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Variables
    let video = document.getElementById('video');
    let canvas = document.getElementById('canvas');
    let ctx = canvas.getContext('2d');
    let cameraOverlay = document.getElementById('cameraOverlay');
    let startCameraBtn = document.getElementById('startCamera');
    let captureGestureBtn = document.getElementById('captureGesture');
    let toggleCameraBtn = document.getElementById('toggleCamera');
    let loadingSpinner = document.getElementById('loadingSpinner');
    let resultContainer = document.getElementById('resultContainer');
    let resultTitle = document.getElementById('resultTitle');
    let resultMessage = document.getElementById('resultMessage');
    let resultImage = document.getElementById('resultImage');
    let logWaterBtn = document.getElementById('logWater');
    let tryAgainBtn = document.getElementById('tryAgain');
    let gestureFeedback = document.getElementById('gestureFeedback');

    let stream = null;
    let facingMode = 'user'; // Start with front camera
    let detectedGesture = null;
    let gestureAmount = 0;
    let gestureTimer = null;
    let isProcessing = false;

    // Start camera
    startCameraBtn.addEventListener('click', async function() {
        try {
            stream = await navigator.mediaDevices.getUserMedia({
                video: { facingMode: facingMode }
            });
            video.srcObject = stream;

            // Hide overlay and enable buttons
            cameraOverlay.style.display = 'none';
            captureGestureBtn.disabled = false;
            toggleCameraBtn.disabled = false;

            // Set canvas size to match video
            video.addEventListener('loadedmetadata', function() {
                canvas.width = video.videoWidth;
                canvas.height = video.videoHeight;
            });

            // Show instruction to use the Capture Gesture button
            updateGestureFeedback('detecting', 'Camera ready. Click "Capture Gesture" when ready.');

        } catch (error) {
            console.error('Error accessing camera:', error);
            alert('Could not access the camera. Please ensure you have granted camera permissions.');
        }
    });

    // Toggle between front and back camera
    toggleCameraBtn.addEventListener('click', async function() {
        if (!stream) return;

        // Stop all tracks
        stream.getTracks().forEach(track => track.stop());

        // Toggle facing mode
        facingMode = facingMode === 'user' ? 'environment' : 'user';

        try {
            stream = await navigator.mediaDevices.getUserMedia({
                video: { facingMode: facingMode }
            });
            video.srcObject = stream;
        } catch (error) {
            console.error('Error toggling camera:', error);
            alert('Could not switch camera. Your device might only have one camera.');

            // Try to revert to the previous camera
            facingMode = facingMode === 'user' ? 'environment' : 'user';
            try {
                stream = await navigator.mediaDevices.getUserMedia({
                    video: { facingMode: facingMode }
                });
                video.srcObject = stream;
            } catch (e) {
                console.error('Error reverting camera:', e);
            }
        }
    });

    // Capture gesture manually
    captureGestureBtn.addEventListener('click', function() {
        if (isProcessing) return;

        // Clear any existing timer to prevent automatic detection
        if (gestureTimer) {
            clearInterval(gestureTimer);
            gestureTimer = null;
        }

        // Process the frame with manual mode
        captureAndProcessFrame(false);
    });

    // Log water button
    logWaterBtn.addEventListener('click', function() {
        if (!detectedGesture) return;

        console.log(`Logging water: ${gestureAmount}ml with gesture ${detectedGesture}`);

        // Create a form to submit the data
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/log_water';
        form.style.display = 'none';

        // Add the amount
        const amountInput = document.createElement('input');
        amountInput.type = 'hidden';
        amountInput.name = 'amount';
        amountInput.value = gestureAmount;
        form.appendChild(amountInput);

        // Add the drink type (default to water)
        const drinkTypeInput = document.createElement('input');
        drinkTypeInput.type = 'hidden';
        drinkTypeInput.name = 'drink_type_id';
        drinkTypeInput.value = '1'; // Water
        form.appendChild(drinkTypeInput);

        // Add notes
        const notesInput = document.createElement('input');
        notesInput.type = 'hidden';
        notesInput.name = 'notes';
        notesInput.value = 'Logged via gesture: ' + detectedGesture;
        form.appendChild(notesInput);

        // Submit the form
        document.body.appendChild(form);
        form.submit();
    });

    // Try again button
    tryAgainBtn.addEventListener('click', function() {
        resultContainer.style.display = 'none';
        detectedGesture = null;
        gestureAmount = 0;

        // Show instruction to use the Capture Gesture button
        updateGestureFeedback('detecting', 'Camera ready. Click "Capture Gesture" when ready.');
    });

    // Start automatic gesture detection - disabled
    function startGestureDetection() {
        // Clear any existing timer
        if (gestureTimer) {
            clearInterval(gestureTimer);
            gestureTimer = null;
        }

        // Set feedback to manual mode
        updateGestureFeedback('detecting', 'Camera ready. Click "Capture Gesture" when ready.');

        // Automatic detection is disabled - we only use manual capture
    }

    // Capture and process a frame
    function captureAndProcessFrame(automatic = false) {
        if (!video.srcObject) return;

        isProcessing = true;

        // Draw the current frame to the canvas
        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

        // Convert the canvas to a blob
        canvas.toBlob(function(blob) {
            // Create a FormData object
            const formData = new FormData();
            formData.append('image', blob, 'gesture.jpg');
            formData.append('automatic', automatic ? 'true' : 'false');

            // Show loading spinner if not in automatic mode
            if (!automatic) {
                loadingSpinner.style.display = 'block';
            }

            // Send the image to the server
            fetch('/api/process_gesture', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                console.log('Gesture processing result:', data);

                if (data.success && data.result && data.result.gesture) {
                    // Handle successful gesture detection
                    handleGestureDetected(data.result, automatic);
                } else {
                    // Handle failed gesture detection
                    if (!automatic) {
                        updateGestureFeedback('error', 'No gesture detected');
                        loadingSpinner.style.display = 'none';

                        resultTitle.textContent = 'No Gesture Detected';
                        resultMessage.textContent = 'Please try again with a clearer gesture.';
                        resultImage.src = data.result?.annotated_image || '';
                        resultContainer.style.display = 'block';
                    }
                }

                isProcessing = false;
            })
            .catch(error => {
                console.error('Error processing gesture:', error);

                if (!automatic) {
                    updateGestureFeedback('error', 'Error processing');
                    loadingSpinner.style.display = 'none';
                    alert('Error processing gesture. Please try again.');
                }

                isProcessing = false;
            });
        }, 'image/jpeg');
    }

    // Handle detected gesture
    function handleGestureDetected(result, automatic) {
        detectedGesture = result.gesture;

        // Set amount based on gesture or use the amount from the result if available
        if (result.amount) {
            // Use the amount from the result
            gestureAmount = result.amount;
            console.log(`Using amount from result: ${gestureAmount}ml`);
        } else {
            // Set amount based on gesture
            if (detectedGesture === 'peace_sign') {
                gestureAmount = 200;
            } else if (detectedGesture === 'thumbs_up') {
                gestureAmount = 330;
            } else {
                gestureAmount = 250; // Default
            }
            console.log(`Using default amount for ${detectedGesture}: ${gestureAmount}ml`);
        }

        // Update feedback
        updateGestureFeedback('success', `${detectedGesture} detected!`);

        // If automatic, only show feedback briefly
        if (automatic) {
            // Stop the detection timer
            if (gestureTimer) {
                clearInterval(gestureTimer);
                gestureTimer = null;
            }

            // Show the result
            loadingSpinner.style.display = 'none';
            resultTitle.textContent = 'Gesture Detected!';
            resultMessage.textContent = `Detected a ${detectedGesture.replace('_', ' ')} gesture. This will log ${gestureAmount}ml of water.`;
            resultImage.src = result.annotated_image || '';
            resultContainer.style.display = 'block';
        } else {
            // Show loading spinner while processing
            loadingSpinner.style.display = 'none';

            // Show the result
            resultTitle.textContent = 'Gesture Detected!';
            resultMessage.textContent = `Detected a ${detectedGesture.replace('_', ' ')} gesture. This will log ${gestureAmount}ml of water.`;
            resultImage.src = result.annotated_image || '';
            resultContainer.style.display = 'block';
        }
    }

    // Update gesture feedback
    function updateGestureFeedback(type, message) {
        gestureFeedback.textContent = message;
        gestureFeedback.className = 'gesture-feedback';

        if (type === 'success') {
            gestureFeedback.classList.add('success');
        } else if (type === 'error') {
            gestureFeedback.classList.add('error');
        } else if (type === 'detecting') {
            gestureFeedback.classList.add('detecting');
        }

        // Show the feedback
        gestureFeedback.style.display = 'block';

        // Hide after a delay if it's a success or error
        if (type === 'success' || type === 'error') {
            setTimeout(() => {
                gestureFeedback.style.display = 'none';
            }, 3000);
        }
    }

    // Clean up when leaving the page
    window.addEventListener('beforeunload', function() {
        if (stream) {
            stream.getTracks().forEach(track => track.stop());
        }

        if (gestureTimer) {
            clearInterval(gestureTimer);
        }
    });
</script>
{% endblock %}
