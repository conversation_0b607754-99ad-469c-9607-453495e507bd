/* Custom styles for HydroMate */

:root {
    --accent-color: #4DA6FF;
    --accent-color-hover: #3a8fd9;
    --bg-color: #f8f9fa;
    --text-color: #212529;
    --card-bg: #ffffff;
    --card-shadow: rgba(0, 0, 0, 0.1);
    --border-color: #dee2e6;
    --footer-bg: #f8f9fa;
    --navbar-bg: var(--accent-color);
    --navbar-text: #ffffff;
}

/* Light Theme (default) */
body.light-theme {
    --bg-color: #f8f9fa;
    --text-color: #212529;
    --card-bg: #ffffff;
    --card-shadow: rgba(0, 0, 0, 0.1);
    --border-color: #dee2e6;
    --footer-bg: #f8f9fa;
}

/* Dark Theme */
body.dark-theme {
    --bg-color: #121212;
    --text-color: #f8f9fa;
    --card-bg: #1e1e1e;
    --card-shadow: rgba(0, 0, 0, 0.3);
    --border-color: #2d2d2d;
    --footer-bg: #1e1e1e;
}

body {
    background-color: var(--bg-color);
    color: var(--text-color);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    transition: background-color 0.3s ease, color 0.3s ease;
    overflow-x: hidden;
    position: relative;
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.navbar-dark {
    background-color: var(--accent-color) !important;
}

.container {
    flex: 1;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

.card {
    background-color: var(--card-bg);
    box-shadow: 0 4px 6px var(--card-shadow);
    border: none;
    border-radius: 10px;
    overflow: hidden;
    transition: background-color 0.3s ease;
}

.card-header {
    border-bottom: none;
    background-color: var(--accent-color);
    color: white;
}

.progress {
    border-radius: 10px;
    overflow: hidden;
    background-color: var(--border-color);
}

.progress-bar {
    background-color: var(--accent-color);
    font-weight: bold;
}

.btn-primary {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}

.btn-primary:hover {
    background-color: var(--accent-color-hover);
    border-color: var(--accent-color-hover);
}

.btn-outline-primary {
    border-color: var(--accent-color);
    color: var(--accent-color);
}

.btn-outline-primary:hover {
    background-color: var(--accent-color);
    border-color: var(--accent-color);
}

footer {
    margin-top: auto;
    background-color: var(--footer-bg);
    border-top: 1px solid var(--border-color);
}

/* Form controls in dark mode */
body.dark-theme .form-control {
    background-color: #2d2d2d;
    border-color: #3d3d3d;
    color: #f8f9fa;
}

body.dark-theme .form-control:focus {
    background-color: #2d2d2d;
    color: #f8f9fa;
}

/* Theme switch styling */
.theme-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.theme-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: var(--accent-color);
}

input:checked + .slider:before {
    transform: translateX(26px);
}

/* Color picker styling */
.color-picker {
    display: inline-block;
    margin-left: 15px;
}

.color-picker select {
    padding: 5px;
    border-radius: 5px;
    border: 1px solid var(--border-color);
    background-color: var(--card-bg);
    color: var(--text-color);
}

/* Dashboard specific styles */
.dashboard-container {
    display: flex;
    flex-direction: column;
    width: 100%;
    margin-top: 20px;
}

.dashboard-row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px;
    width: 100%;
}

.dashboard-card {
    margin-bottom: 15px;
    width: 100%;
}

/* More compact dashboard */
.card-body {
    padding: 0.75rem;
}

.mb-3 {
    margin-bottom: 0.75rem !important;
}

.mb-4 {
    margin-bottom: 1rem !important;
}

/* Weather widget styles */
.weather-header-container {
    margin-bottom: 0;
}

.weather-widget {
    margin-top: 0;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}

.drink-type-badge {
    display: inline-block;
    padding: 3px 8px;
    margin: 3px;
    border-radius: 12px;
    color: white;
    font-weight: bold;
    font-size: 0.85rem;
}

/* Fix for container images */
.container-img {
    height: 100px;
    object-fit: cover;
}

.container-placeholder {
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    color: var(--accent-color);
}

/* Feature card styles */
.feature-card {
    transition: transform 0.2s;
}

.feature-card:hover {
    transform: translateY(-3px);
}

.feature-icon {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

/* Fix for graph display */
canvas {
    max-width: 100%;
    height: auto !important;
}

/* Fix for milk and other drink types */
.drink-type-milk {
    background-color: #f5f5f5 !important;
    color: #333 !important;
    border: 1px solid #ddd;
}

/* Responsive fixes */
@media (max-width: 768px) {
    .dashboard-row {
        flex-direction: column;
    }

    .navbar-brand img {
        height: 30px;
    }
}
