"""
Simple Gesture Logger - Simulates gesture detection without requiring OpenCV or MediaPipe
This script provides a simple GUI for selecting gestures
"""

import tkinter as tk
from tkinter import ttk
import time
import json
import os
from datetime import datetime

# Constants for gesture types
PEACE_SIGN = "peace_sign"  # ✌️ - logs 200ml
THUMBS_UP = "thumbs_up"    # 👍 - logs 330ml
NO_GESTURE = "no_gesture"  # No gesture detected

class SimpleGestureLogger:
    """Simple gesture logger using Tkinter"""

    def __init__(self):
        """Initialize the logger"""
        self.result = None
        self.selected_gesture = tk.StringVar(value=NO_GESTURE)

    def show_gui(self):
        """Show the GUI for selecting gestures"""
        # Create the main window
        self.root = tk.Tk()
        self.root.title("Gesture Logger")
        self.root.geometry("500x450")
        self.root.resizable(False, False)

        # Set up the UI
        self._setup_ui()

        # Start the main loop
        self.root.mainloop()

        # Return the selected gesture
        return self.result

    def _setup_ui(self):
        """Set up the UI elements"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Title
        title_label = ttk.Label(
            main_frame,
            text="Gesture Logging",
            font=("Arial", 18, "bold")
        )
        title_label.pack(pady=10)

        # Instructions
        instructions_label = ttk.Label(
            main_frame,
            text="Select a gesture to log water intake:",
            font=("Arial", 12)
        )
        instructions_label.pack(pady=10)

        # Camera simulation frame
        camera_frame = ttk.LabelFrame(main_frame, text="Camera Simulation")
        camera_frame.pack(pady=10, fill=tk.X)

        # Gesture selection frame
        gesture_frame = ttk.Frame(camera_frame, padding=10)
        gesture_frame.pack(fill=tk.X)

        # No gesture radio button (default)
        no_gesture_radio = ttk.Radiobutton(
            gesture_frame,
            text="No Gesture Detected",
            variable=self.selected_gesture,
            value=NO_GESTURE
        )
        no_gesture_radio.grid(row=0, column=0, padx=10, pady=5, sticky="w")

        # Peace sign radio button
        peace_sign_radio = ttk.Radiobutton(
            gesture_frame,
            text="Peace Sign ✌️ (200ml)",
            variable=self.selected_gesture,
            value=PEACE_SIGN
        )
        peace_sign_radio.grid(row=1, column=0, padx=10, pady=5, sticky="w")

        # Thumbs up radio button
        thumbs_up_radio = ttk.Radiobutton(
            gesture_frame,
            text="Thumbs Up 👍 (330ml)",
            variable=self.selected_gesture,
            value=THUMBS_UP
        )
        thumbs_up_radio.grid(row=2, column=0, padx=10, pady=5, sticky="w")

        # Buttons frame
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(pady=20)

        # Detect gesture button
        detect_button = ttk.Button(
            buttons_frame,
            text="Detect Gesture",
            command=self._detect_gesture
        )
        detect_button.grid(row=0, column=0, padx=10, pady=10)

        # Cancel button
        cancel_button = ttk.Button(
            buttons_frame,
            text="Cancel",
            command=self._cancel
        )
        cancel_button.grid(row=0, column=1, padx=10, pady=10)

        # Status label
        self.status_label = ttk.Label(
            main_frame,
            text="Select a gesture and click 'Detect Gesture'",
            font=("Arial", 10, "italic")
        )
        self.status_label.pack(pady=10)

        # Error label (hidden by default)
        self.error_label = ttk.Label(
            main_frame,
            text="",
            font=("Arial", 10),
            foreground="red"
        )
        self.error_label.pack(pady=5)

        # Set a timeout
        self.root.after(30000, self._timeout)

    def _detect_gesture(self):
        """Handle gesture detection"""
        selected = self.selected_gesture.get()

        if selected == NO_GESTURE:
            # Show error if no gesture is selected
            self.error_label.config(text="No gesture detected. Please try again or select a gesture.")
            return

        # Clear any error
        self.error_label.config(text="")

        # Show confirmation
        self.status_label.config(text=f"Detected: {selected.replace('_', ' ')}")

        # Show confirmation
        amount = "200ml" if selected == PEACE_SIGN else "330ml"
        confirmation_text = f"Logging {amount} of water..."
        self.status_label.config(text=confirmation_text)

        # Update the UI
        self.root.update()

        # Wait a moment for visual feedback
        time.sleep(1.5)

        # Set the result and close the window
        self.result = selected
        self.root.destroy()

    def _cancel(self):
        """Handle cancellation"""
        self.result = None
        self.root.destroy()

    def _timeout(self):
        """Handle timeout"""
        self.status_label.config(text="Timeout: No gesture selected")

        # Wait a moment for visual feedback
        time.sleep(1.5)

        # Close the window
        self.result = None
        self.root.destroy()

def main():
    """Main function to run the gesture logger"""
    logger = SimpleGestureLogger()
    gesture = logger.show_gui()

    if gesture and gesture != NO_GESTURE:
        print(f"GESTURE_DETECTED:{gesture}")

        # Create a result file for the app to read
        result = {
            "success": True,
            "gesture": gesture,
            "timestamp": datetime.now().isoformat(),
            "amount": 200 if gesture == PEACE_SIGN else 330
        }

        # Save the result to a file
        with open("gesture_result.json", "w") as f:
            json.dump(result, f)

        return gesture
    else:
        print("NO_GESTURE_DETECTED")

        # Create a result file for the app to read
        result = {
            "success": False,
            "gesture": None,
            "timestamp": datetime.now().isoformat()
        }

        # Save the result to a file
        with open("gesture_result.json", "w") as f:
            json.dump(result, f)

        return None

if __name__ == "__main__":
    main()
