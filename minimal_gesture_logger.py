
import tkinter as tk
from tkinter import messagebox
import json
from datetime import datetime

def main():
    root = tk.Tk()
    root.withdraw()
    result = messagebox.askyesno(
        "Gesture Detection",
        "Would you like to log 200ml of water with a peace sign gesture?"
    )
    if result:
        # Create the result data
        result_data = {
            "success": True,
            "gesture": "peace_sign",
            "timestamp": datetime.now().isoformat(),
            "amount": 200
        }
        
        # Save the result to a file
        with open("gesture_result.json", "w") as f:
            json.dump(result_data, f)
        
        print("GESTURE_DETECTED:peace_sign")
        return True
    else:
        # Create the result data
        result_data = {
            "success": False,
            "gesture": None,
            "timestamp": datetime.now().isoformat()
        }
        
        # Save the result to a file
        with open("gesture_result.json", "w") as f:
            json.dump(result_data, f)
        
        print("NO_GESTURE_DETECTED")
        return False

if __name__ == "__main__":
    main()
        