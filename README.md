# HydroMate - Water Intake Tracker

A smart water intake tracking application with advanced features including container recognition, OCR label reading, voice input, and more.

## Features

- **Smart Object Recognition**: Recognize containers using computer vision
- **OCR Label Reading**: Read bottle labels to extract volume information
- **Voice Input**: Log water intake using voice commands
- **Drink Type Classification**: Track different types of drinks with hydration factors
- **Dynamic Logo**: Logo updates based on your daily progress
- **Profile Avatars**: Customize your profile with avatars

## Installation

1. Clone the repository:
```
git clone https://github.com/yourusername/water-intake-tracker.git
cd water-intake-tracker
```

2. Install the required dependencies:
```
pip install -r requirements.txt
```

3. Generate default avatars:
```
python water_tracker/generate_simple_avatars.py
```

## Running the Application

### Windows
Double-click the `run.bat` file or run:
```
python run.py
```

### macOS/Linux
```
python run.py
```

The application will be available at http://127.0.0.1:8080

## Optional Dependencies

For full functionality, you may want to install:

- **Tesseract OCR**: For label reading functionality
  - Windows: Download from https://github.com/UB-Mannheim/tesseract/wiki
  - macOS: `brew install tesseract`
  - Linux: `sudo apt install tesseract-ocr`

- **FFmpeg**: For voice recognition
  - Windows: Download from https://ffmpeg.org/download.html
  - macOS: `brew install ffmpeg`
  - Linux: `sudo apt install ffmpeg`

## Usage

1. Register an account or use the demo account:
   - Username: demo
   - Password: demo123

2. Log your water intake using the dashboard
3. Track your progress with charts and statistics
4. Use smart features like container recognition and voice input

## Troubleshooting

If you encounter any issues:

1. Make sure all dependencies are installed
2. Check that the database file exists
3. Ensure Tesseract OCR is installed for label reading
4. Verify FFmpeg is installed for voice recognition

## License

This project is licensed under the MIT License - see the LICENSE file for details.
