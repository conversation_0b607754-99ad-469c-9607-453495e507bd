"""
Simple gesture recognition module using only OpenCV
This module can be used as a fallback when MediaPipe is not available
"""

import os
import cv2
import numpy as np
import logging
from datetime import datetime

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SimpleGestureDetector:
    """Class for detecting hand gestures using only OpenCV"""
    
    def __init__(self):
        """Initialize the gesture detector"""
        logger.info("Initializing SimpleGestureDetector with OpenCV")
        
        # Check OpenCV version
        opencv_version = cv2.__version__
        logger.info(f"OpenCV version: {opencv_version}")
    
    def process_image(self, image_path):
        """Process an image and detect gestures"""
        try:
            # Check if the image exists
            if not os.path.exists(image_path):
                logger.error(f"Image file not found: {image_path}")
                return {
                    'success': False,
                    'error': f"Image file not found: {image_path}",
                    'gesture': None
                }
            
            # Read the image
            image = cv2.imread(image_path)
            if image is None:
                logger.error(f"Failed to read image: {image_path}")
                return {
                    'success': False,
                    'error': f"Failed to read image: {image_path}",
                    'gesture': None
                }
            
            # Process the image
            success, processed_image, gesture = self._detect_hand_simple(image)
            
            # Save the processed image
            output_path = image_path + "_processed.jpg"
            cv2.imwrite(output_path, processed_image)
            
            # Return the result
            if success:
                return {
                    'success': True,
                    'gesture': gesture.lower().replace(' ', '_'),
                    'confidence': 0.7,
                    'annotated_image': output_path
                }
            else:
                return {
                    'success': False,
                    'error': "No hand detected",
                    'gesture': None,
                    'annotated_image': output_path
                }
            
        except Exception as e:
            logger.error(f"Error processing image: {e}")
            import traceback
            traceback.print_exc()
            return {
                'success': False,
                'error': str(e),
                'gesture': None
            }
    
    def _detect_hand_simple(self, image):
        """
        Simple hand detection using color thresholding and contour analysis
        """
        # Make a copy of the image
        result_image = image.copy()
        
        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Apply Gaussian blur
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        
        # Use adaptive thresholding
        thresh = cv2.adaptiveThreshold(
            blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
            cv2.THRESH_BINARY_INV, 11, 2
        )
        
        # Find contours
        contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # If no contours found, try a different approach
        if not contours:
            # Try simple thresholding
            _, thresh = cv2.threshold(gray, 127, 255, cv2.THRESH_BINARY)
            contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # Find the largest contour (assuming it's the hand)
        if contours:
            hand_contour = max(contours, key=cv2.contourArea)
            
            # Get bounding rectangle
            x, y, w, h = cv2.boundingRect(hand_contour)
            
            # Draw rectangle around hand
            cv2.rectangle(result_image, (x, y), (x+w, y+h), (0, 255, 0), 2)
            
            # Get convex hull and defects for finger detection
            hull = cv2.convexHull(hand_contour, returnPoints=False)
            
            # Check if hull has enough points
            if len(hull) > 3:
                try:
                    defects = cv2.convexityDefects(hand_contour, hull)
                    
                    # Count fingers based on defects
                    finger_count = 0
                    
                    if defects is not None:
                        for i in range(defects.shape[0]):
                            s, e, f, d = defects[i, 0]
                            start = tuple(hand_contour[s][0])
                            end = tuple(hand_contour[e][0])
                            far = tuple(hand_contour[f][0])
                            
                            # Calculate angle between fingers
                            a = np.sqrt((end[0] - start[0]) ** 2 + (end[1] - start[1]) ** 2)
                            b = np.sqrt((far[0] - start[0]) ** 2 + (far[1] - start[1]) ** 2)
                            c = np.sqrt((end[0] - far[0]) ** 2 + (end[1] - far[1]) ** 2)
                            
                            # Avoid division by zero
                            if b * c == 0:
                                continue
                                
                            angle = np.arccos((b ** 2 + c ** 2 - a ** 2) / (2 * b * c))
                            
                            # If angle is less than 90 degrees, it's likely a finger
                            if angle <= np.pi / 2:
                                finger_count += 1
                                # Mark the defect point
                                cv2.circle(result_image, far, 5, [0, 0, 255], -1)
                    
                    # Add 1 for the thumb
                    finger_count += 1
                    
                    # Determine gesture based on finger count
                    gesture = "Unknown"
                    if finger_count == 2:
                        gesture = "Peace Sign"
                    elif finger_count == 1:
                        gesture = "Thumbs Up"
                    elif finger_count == 5:
                        gesture = "Open Hand"
                    
                    # Add text to image
                    cv2.putText(result_image, f"Gesture: {gesture} ({finger_count} fingers)", 
                                (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 255, 0), 2)
                    
                    return True, result_image, gesture
                except Exception as e:
                    logger.error(f"Error in defect analysis: {e}")
        
        # If no hand detected or processing failed
        cv2.putText(result_image, "No hand detected", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
        return False, result_image, "None"

# Create a global instance
gesture_detector = SimpleGestureDetector()
