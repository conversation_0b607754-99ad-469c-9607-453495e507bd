"""
Test Flask app for gesture recognition
"""

import os
import sys
from flask import Flask, render_template, request, jsonify, url_for
from werkzeug.utils import secure_filename
from datetime import datetime

# Import the gesture detector
from simple_gesture_recognition import gesture_detector

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'test_secret_key'
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max upload

# Create upload folder if it doesn't exist
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

@app.route('/')
def index():
    """Home page"""
    return render_template('index.html')

@app.route('/gesture_test')
def gesture_test():
    """Gesture test page"""
    return render_template('gesture_test.html')

@app.route('/api/process_gesture', methods=['POST'])
def process_gesture():
    """API endpoint to process gesture images"""
    # Check if an image was uploaded
    if 'image' not in request.files:
        return jsonify({'error': 'No image provided'}), 400

    image_file = request.files['image']
    
    if image_file.filename == '':
        return jsonify({'error': 'No image selected'}), 400

    try:
        # Save the uploaded image
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        filename = secure_filename(f"gesture_{timestamp}.jpg")
        image_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        image_file.save(image_path)

        # Process the image with the gesture detector
        result = gesture_detector.process_image(image_path)

        # If successful, prepare the result
        if result.get('success'):
            # Get the relative path for the annotated image
            annotated_image = result.get('annotated_image')
            if annotated_image and os.path.exists(annotated_image):
                # Get the relative URL for the static file
                result['annotated_image'] = f"/uploads/{os.path.basename(annotated_image)}"

            # Return the result
            return jsonify({
                'success': True,
                'result': result
            })
        else:
            # Return the error
            return jsonify({
                'success': False,
                'result': result
            })

    except Exception as e:
        import traceback
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500

# Serve uploaded files
@app.route('/uploads/<filename>')
def uploaded_file(filename):
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

if __name__ == '__main__':
    # Create templates directory if it doesn't exist
    os.makedirs('templates', exist_ok=True)
    
    # Create a simple index.html template
    with open('templates/index.html', 'w') as f:
        f.write("""
<!DOCTYPE html>
<html>
<head>
    <title>Gesture Recognition Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
        h1 { color: #333; }
        .container { max-width: 800px; margin: 0 auto; }
        .btn { 
            display: inline-block; 
            padding: 10px 20px; 
            background-color: #007bff; 
            color: white; 
            text-decoration: none; 
            border-radius: 4px; 
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Gesture Recognition Test</h1>
        <p>This is a test application for gesture recognition using OpenCV.</p>
        <a href="/gesture_test" class="btn">Try Gesture Recognition</a>
    </div>
</body>
</html>
        """)
    
    # Create a gesture_test.html template
    with open('templates/gesture_test.html', 'w') as f:
        f.write("""
<!DOCTYPE html>
<html>
<head>
    <title>Gesture Recognition Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
        h1 { color: #333; }
        .container { max-width: 800px; margin: 0 auto; }
        .camera-container {
            position: relative;
            width: 100%;
            max-width: 640px;
            margin: 0 auto;
            overflow: hidden;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }
        #video {
            width: 100%;
            height: auto;
            background-color: #f0f0f0;
            display: block;
        }
        #canvas {
            display: none;
        }
        .camera-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            background-color: rgba(0, 0, 0, 0.5);
            color: white;
            text-align: center;
            z-index: 10;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            border: none;
            cursor: pointer;
        }
        .btn:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .result-container {
            display: none;
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        .result-image {
            max-width: 100%;
            border-radius: 8px;
            margin-top: 10px;
        }
        .loading-spinner {
            display: none;
            margin: 20px auto;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Gesture Recognition Test</h1>
        
        <div class="camera-container">
            <video id="video" autoplay playsinline></video>
            <canvas id="canvas"></canvas>
            <div id="cameraOverlay" class="camera-overlay">
                <h4>Camera Access Required</h4>
                <p>Click the button below to start the camera</p>
                <button id="startCamera" class="btn">Start Camera</button>
            </div>
        </div>
        
        <div style="margin-top: 15px; text-align: center;">
            <button id="captureGesture" class="btn" disabled>Capture Gesture</button>
            <button id="toggleCamera" class="btn" disabled>Switch Camera</button>
        </div>
        
        <div id="loadingSpinner" class="loading-spinner">
            <div style="border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; width: 30px; height: 30px; animation: spin 2s linear infinite; margin: 0 auto;"></div>
            <p>Processing gesture...</p>
        </div>
        
        <div id="resultContainer" class="result-container">
            <h3 id="resultTitle">Gesture Detected</h3>
            <p id="resultMessage"></p>
            <img id="resultImage" class="result-image" src="" alt="Detected Gesture">
            <div style="margin-top: 15px;">
                <button id="tryAgain" class="btn">Try Again</button>
            </div>
        </div>
    </div>
    
    <script>
        // Variables
        let video = document.getElementById('video');
        let canvas = document.getElementById('canvas');
        let ctx = canvas.getContext('2d');
        let cameraOverlay = document.getElementById('cameraOverlay');
        let startCameraBtn = document.getElementById('startCamera');
        let captureGestureBtn = document.getElementById('captureGesture');
        let toggleCameraBtn = document.getElementById('toggleCamera');
        let loadingSpinner = document.getElementById('loadingSpinner');
        let resultContainer = document.getElementById('resultContainer');
        let resultTitle = document.getElementById('resultTitle');
        let resultMessage = document.getElementById('resultMessage');
        let resultImage = document.getElementById('resultImage');
        let tryAgainBtn = document.getElementById('tryAgain');

        let stream = null;
        let facingMode = 'user'; // Start with front camera

        // Start camera
        startCameraBtn.addEventListener('click', async function() {
            try {
                stream = await navigator.mediaDevices.getUserMedia({
                    video: { facingMode: facingMode }
                });
                video.srcObject = stream;
                
                // Hide overlay and enable buttons
                cameraOverlay.style.display = 'none';
                captureGestureBtn.disabled = false;
                toggleCameraBtn.disabled = false;
                
                // Set canvas size to match video
                video.addEventListener('loadedmetadata', function() {
                    canvas.width = video.videoWidth;
                    canvas.height = video.videoHeight;
                });
                
            } catch (error) {
                console.error('Error accessing camera:', error);
                alert('Could not access the camera. Please ensure you have granted camera permissions.');
            }
        });

        // Toggle between front and back camera
        toggleCameraBtn.addEventListener('click', async function() {
            if (!stream) return;
            
            // Stop all tracks
            stream.getTracks().forEach(track => track.stop());
            
            // Toggle facing mode
            facingMode = facingMode === 'user' ? 'environment' : 'user';
            
            try {
                stream = await navigator.mediaDevices.getUserMedia({
                    video: { facingMode: facingMode }
                });
                video.srcObject = stream;
            } catch (error) {
                console.error('Error toggling camera:', error);
                alert('Could not switch camera. Your device might only have one camera.');
                
                // Try to revert to the previous camera
                facingMode = facingMode === 'user' ? 'environment' : 'user';
                try {
                    stream = await navigator.mediaDevices.getUserMedia({
                        video: { facingMode: facingMode }
                    });
                    video.srcObject = stream;
                } catch (e) {
                    console.error('Error reverting camera:', e);
                }
            }
        });

        // Capture gesture
        captureGestureBtn.addEventListener('click', function() {
            if (!video.srcObject) return;
            
            // Show loading spinner
            loadingSpinner.style.display = 'block';
            
            // Draw the current frame to the canvas
            ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
            
            // Convert the canvas to a blob
            canvas.toBlob(function(blob) {
                // Create a FormData object
                const formData = new FormData();
                formData.append('image', blob, 'gesture.jpg');
                
                // Send the image to the server
                fetch('/api/process_gesture', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    console.log('Gesture processing result:', data);
                    
                    // Hide loading spinner
                    loadingSpinner.style.display = 'none';
                    
                    if (data.success && data.result && data.result.gesture) {
                        // Show success result
                        resultTitle.textContent = 'Gesture Detected!';
                        resultMessage.textContent = `Detected a ${data.result.gesture.replace('_', ' ')} gesture.`;
                        resultImage.src = data.result.annotated_image;
                    } else {
                        // Show error result
                        resultTitle.textContent = 'No Gesture Detected';
                        resultMessage.textContent = data.result?.error || 'Could not detect a gesture. Please try again.';
                        resultImage.src = data.result?.annotated_image || '';
                    }
                    
                    // Show result container
                    resultContainer.style.display = 'block';
                })
                .catch(error => {
                    console.error('Error processing gesture:', error);
                    
                    // Hide loading spinner
                    loadingSpinner.style.display = 'none';
                    
                    // Show error result
                    resultTitle.textContent = 'Error';
                    resultMessage.textContent = 'An error occurred while processing the gesture. Please try again.';
                    resultContainer.style.display = 'block';
                });
            }, 'image/jpeg');
        });

        // Try again button
        tryAgainBtn.addEventListener('click', function() {
            resultContainer.style.display = 'none';
        });

        // Clean up when leaving the page
        window.addEventListener('beforeunload', function() {
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
            }
        });
    </script>
</body>
</html>
        """)
    
    # Add the missing import
    from flask import send_from_directory
    
    # Run the app
    print("Starting Flask app on http://127.0.0.1:5000")
    app.run(debug=True, port=5000)
