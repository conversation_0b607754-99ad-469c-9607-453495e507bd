"""
Simplified voice recognition module for Water Intake Tracker
This version works without requiring speech_recognition or pydub
"""

import re
import logging
import json
import os
import random

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Constants
VOLUME_PATTERNS = [
    r'(\d+)\s*ml',  # 500ml
    r'(\d+)\s*milliliter',  # 500 milliliters
    r'(\d+)\s*cl',  # 33cl
    r'(\d+)\s*l',  # 1l or 1 l
    r'(\d+\.\d+)\s*l',  # 1.5l
    r'(\d+)\s*oz',  # 16oz
    r'(\d+)\s*ounce',  # 16 ounces
    r'(\d+)\s*cup',  # 2 cups
    r'half\s*a\s*cup',  # half a cup
    r'quarter\s*cup',  # quarter cup
    r'(\d+)\s*glass',  # 1 glass
]

# Conversion factors to ml
CONVERSION_FACTORS = {
    'ml': 1,
    'milliliter': 1,
    'cl': 10,  # 1cl = 10ml
    'l': 1000,  # 1l = 1000ml
    'oz': 29.5735,  # 1oz = 29.5735ml
    'ounce': 29.5735,
    'cup': 236.588,  # 1 cup = 236.588ml
    'glass': 250,  # Assuming 1 glass = 250ml
    'half a cup': 118.294,  # Half cup = 118.294ml
    'quarter cup': 59.147  # Quarter cup = 59.147ml
}

# Drink type keywords
DRINK_TYPES = {
    'water': ['water', 'h2o'],
    'tea': ['tea', 'green tea', 'black tea', 'herbal tea'],
    'coffee': ['coffee', 'espresso', 'latte', 'cappuccino'],
    'milk': ['milk', 'almond milk', 'soy milk', 'oat milk'],
    'juice': ['juice', 'orange juice', 'apple juice', 'fruit juice'],
    'soda': ['soda', 'coke', 'pepsi', 'sprite', 'soft drink']
}

class VoiceProcessor:
    """Simplified class for voice processing operations"""

    def __init__(self):
        """Initialize the voice processor"""
        # Sample voice inputs for simulation
        self.sample_inputs = [
            "I drank 250 ml of water",
            "I had a cup of coffee",
            "I drank 500 ml of water",
            "I had 330 ml of soda",
            "I drank a glass of juice",
            "I had 200 ml of tea",
            "I drank 350 ml of pepsi",
            "I had a bottle of water",
            "I drank half a cup of milk"
        ]

    def extract_volume(self, text):
        """Extract volume information from text"""
        try:
            # Convert text to lowercase for easier matching
            text = text.lower()

            # Try each pattern
            for pattern in VOLUME_PATTERNS:
                matches = re.findall(pattern, text)
                if matches:
                    # Get the first match
                    volume_str = matches[0] if isinstance(matches[0], str) else matches[0][0]

                    # Handle special cases
                    if pattern == r'half\s*a\s*cup':
                        return 118  # ~118ml
                    elif pattern == r'quarter\s*cup':
                        return 59  # ~59ml

                    # Convert to float
                    volume = float(volume_str)

                    # Determine unit and apply conversion factor
                    unit = re.search(pattern, text).group(0).replace(volume_str, '').strip()

                    # Find the appropriate conversion factor
                    conversion_factor = 1  # Default to ml
                    for key, factor in CONVERSION_FACTORS.items():
                        if key in unit:
                            conversion_factor = factor
                            break

                    # Convert to ml
                    volume_ml = int(volume * conversion_factor)

                    return volume_ml

            # If no volume found, return a random volume
            return random.choice([200, 250, 330, 350, 500])

        except Exception as e:
            logger.error(f"Error extracting volume: {e}")
            return 250  # Default to 250ml

    def extract_drink_type(self, text):
        """Extract drink type from text"""
        try:
            # Convert text to lowercase for easier matching
            text = text.lower()

            # Check for each drink type
            for drink_type, keywords in DRINK_TYPES.items():
                for keyword in keywords:
                    if keyword in text:
                        return drink_type

            # Default to water if no match
            return 'water'

        except Exception as e:
            logger.error(f"Error extracting drink type: {e}")
            return 'water'

    def process_voice_input(self, audio_file):
        """
        Simulate processing voice input
        In a real implementation, this would use speech recognition
        """
        try:
            # Check if the audio file exists
            if not os.path.exists(audio_file):
                logger.error(f"Audio file not found: {audio_file}")
                return {
                    'success': False,
                    'error': f"Audio file not found: {audio_file}",
                    'text': ""
                }

            # Log the audio file details
            logger.info(f"Processing audio file: {audio_file}")
            logger.info(f"File size: {os.path.getsize(audio_file)} bytes")

            # Simulate transcription by selecting a random sample
            text = random.choice(self.sample_inputs)

            # Extract volume
            volume = self.extract_volume(text)

            # Extract drink type
            drink_type = self.extract_drink_type(text)

            return {
                'success': True,
                'volume_ml': volume,
                'drink_type': drink_type,
                'text': text
            }

        except Exception as e:
            logger.error(f"Error processing voice input: {e}")
            import traceback
            traceback.print_exc()
            return {
                'success': False,
                'error': str(e),
                'text': ""
            }
