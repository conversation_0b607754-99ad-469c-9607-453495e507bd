"""
Test Gesture Recognition - Standalone testing script for gesture recognition
This script tests the gesture recognition functionality without the main application
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
import os
import sys
import subprocess
from datetime import datetime

# Constants for gesture types
PEACE_SIGN = "peace_sign"  # ✌️ - logs 200ml
THUMBS_UP = "thumbs_up"    # 👍 - logs 330ml
NO_GESTURE = "no_gesture"  # No gesture detected

class GestureTestApp:
    """Test application for gesture recognition"""
    
    def __init__(self, root):
        """Initialize the test application"""
        self.root = root
        self.root.title("Gesture Recognition Test")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # Set up the UI
        self._setup_ui()
        
    def _setup_ui(self):
        """Set up the UI elements"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = ttk.Label(
            main_frame, 
            text="Gesture Recognition Test", 
            font=("Arial", 18, "bold")
        )
        title_label.pack(pady=10)
        
        # Instructions
        instructions_label = ttk.Label(
            main_frame,
            text="This page tests the gesture recognition functionality.",
            font=("Arial", 12)
        )
        instructions_label.pack(pady=10)
        
        # Test options frame
        options_frame = ttk.LabelFrame(main_frame, text="Test Options")
        options_frame.pack(pady=10, fill=tk.X)
        
        # Test options
        self.test_option = tk.StringVar(value="simple_logger")
        
        # Simple logger option
        simple_logger_radio = ttk.Radiobutton(
            options_frame,
            text="Test Simple Gesture Logger (Tkinter UI)",
            variable=self.test_option,
            value="simple_logger"
        )
        simple_logger_radio.grid(row=0, column=0, padx=10, pady=5, sticky="w")
        
        # Minimal logger option
        minimal_logger_radio = ttk.Radiobutton(
            options_frame,
            text="Test Minimal Gesture Logger (Message Box)",
            variable=self.test_option,
            value="minimal_logger"
        )
        minimal_logger_radio.grid(row=1, column=0, padx=10, pady=5, sticky="w")
        
        # Direct selection option
        direct_selection_radio = ttk.Radiobutton(
            options_frame,
            text="Test Direct Gesture Selection",
            variable=self.test_option,
            value="direct_selection"
        )
        direct_selection_radio.grid(row=2, column=0, padx=10, pady=5, sticky="w")
        
        # Test button
        test_button = ttk.Button(
            main_frame,
            text="Run Test",
            command=self._run_test
        )
        test_button.pack(pady=20)
        
        # Results frame
        self.results_frame = ttk.LabelFrame(main_frame, text="Test Results")
        self.results_frame.pack(pady=10, fill=tk.BOTH, expand=True)
        
        # Results text
        self.results_text = tk.Text(self.results_frame, wrap=tk.WORD, height=15)
        self.results_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Direct selection frame (hidden by default)
        self.direct_selection_frame = ttk.LabelFrame(main_frame, text="Direct Gesture Selection")
        
        # Direct selection options
        self.direct_gesture = tk.StringVar(value=NO_GESTURE)
        
        # No gesture option
        no_gesture_radio = ttk.Radiobutton(
            self.direct_selection_frame,
            text="No Gesture Detected",
            variable=self.direct_gesture,
            value=NO_GESTURE
        )
        no_gesture_radio.grid(row=0, column=0, padx=10, pady=5, sticky="w")
        
        # Peace sign option
        peace_sign_radio = ttk.Radiobutton(
            self.direct_selection_frame,
            text="Peace Sign ✌️ (200ml)",
            variable=self.direct_gesture,
            value=PEACE_SIGN
        )
        peace_sign_radio.grid(row=1, column=0, padx=10, pady=5, sticky="w")
        
        # Thumbs up option
        thumbs_up_radio = ttk.Radiobutton(
            self.direct_selection_frame,
            text="Thumbs Up 👍 (330ml)",
            variable=self.direct_gesture,
            value=THUMBS_UP
        )
        thumbs_up_radio.grid(row=2, column=0, padx=10, pady=5, sticky="w")
        
        # Submit button
        submit_button = ttk.Button(
            self.direct_selection_frame,
            text="Submit Gesture",
            command=self._submit_direct_gesture
        )
        submit_button.grid(row=3, column=0, padx=10, pady=10)
        
    def _run_test(self):
        """Run the selected test"""
        test_option = self.test_option.get()
        
        # Clear previous results
        self.results_text.delete(1.0, tk.END)
        self.results_text.insert(tk.END, f"Running test: {test_option}\n\n")
        
        # Hide direct selection frame
        self.direct_selection_frame.pack_forget()
        
        if test_option == "simple_logger":
            self._test_simple_logger()
        elif test_option == "minimal_logger":
            self._test_minimal_logger()
        elif test_option == "direct_selection":
            self._test_direct_selection()
    
    def _test_simple_logger(self):
        """Test the simple gesture logger"""
        self.results_text.insert(tk.END, "Testing simple gesture logger...\n")
        
        # Check if the simple gesture logger exists
        if not os.path.exists("simple_gesture_logger.py"):
            self.results_text.insert(tk.END, "Error: simple_gesture_logger.py not found.\n")
            return
        
        # Run the simple gesture logger
        try:
            self.results_text.insert(tk.END, "Launching simple gesture logger...\n")
            self.root.update()
            
            result = subprocess.run(
                [sys.executable, "simple_gesture_logger.py"],
                capture_output=True,
                text=True
            )
            
            # Display the output
            self.results_text.insert(tk.END, f"Output:\n{result.stdout}\n\n")
            
            # Check if a gesture was detected
            if "GESTURE_DETECTED" in result.stdout:
                gesture_line = [line for line in result.stdout.split('\n') if "GESTURE_DETECTED" in line][0]
                gesture = gesture_line.split(':')[1].strip()
                self.results_text.insert(tk.END, f"Detected gesture: {gesture}\n")
            else:
                self.results_text.insert(tk.END, "No gesture detected.\n")
            
            # Check if the result file exists
            if os.path.exists("gesture_result.json"):
                with open("gesture_result.json", "r") as f:
                    result_data = json.load(f)
                
                self.results_text.insert(tk.END, f"Result file content:\n{json.dumps(result_data, indent=2)}\n\n")
                
                if result_data.get('success', False):
                    self.results_text.insert(tk.END, f"Success: {result_data.get('success')}\n")
                    self.results_text.insert(tk.END, f"Gesture: {result_data.get('gesture')}\n")
                    self.results_text.insert(tk.END, f"Amount: {result_data.get('amount')} ml\n")
                else:
                    self.results_text.insert(tk.END, "Result file indicates no gesture was detected.\n")
            else:
                self.results_text.insert(tk.END, "No result file found.\n")
            
        except Exception as e:
            self.results_text.insert(tk.END, f"Error: {str(e)}\n")
    
    def _test_minimal_logger(self):
        """Test the minimal gesture logger"""
        self.results_text.insert(tk.END, "Testing minimal gesture logger...\n")
        
        # Create a minimal version of the gesture logger
        minimal_logger = """
import tkinter as tk
from tkinter import messagebox

def main():
    root = tk.Tk()
    root.withdraw()
    result = messagebox.askyesno(
        "Gesture Detection",
        "Would you like to log 200ml of water with a peace sign gesture?"
    )
    if result:
        print("GESTURE_DETECTED:peace_sign")
        return True
    else:
        print("NO_GESTURE_DETECTED")
        return False

if __name__ == "__main__":
    main()
        """
        
        # Save the minimal logger
        with open("minimal_gesture_logger.py", "w") as f:
            f.write(minimal_logger)
        
        # Run the minimal logger
        try:
            self.results_text.insert(tk.END, "Launching minimal gesture logger...\n")
            self.root.update()
            
            result = subprocess.run(
                [sys.executable, "minimal_gesture_logger.py"],
                capture_output=True,
                text=True
            )
            
            # Display the output
            self.results_text.insert(tk.END, f"Output:\n{result.stdout}\n\n")
            
            # Check if a gesture was detected
            if "GESTURE_DETECTED" in result.stdout:
                gesture_line = [line for line in result.stdout.split('\n') if "GESTURE_DETECTED" in line][0]
                gesture = gesture_line.split(':')[1].strip()
                self.results_text.insert(tk.END, f"Detected gesture: {gesture}\n")
            else:
                self.results_text.insert(tk.END, "No gesture detected.\n")
            
        except Exception as e:
            self.results_text.insert(tk.END, f"Error: {str(e)}\n")
    
    def _test_direct_selection(self):
        """Test direct gesture selection"""
        self.results_text.insert(tk.END, "Testing direct gesture selection...\n")
        self.results_text.insert(tk.END, "Please select a gesture and click 'Submit Gesture'.\n")
        
        # Show direct selection frame
        self.direct_selection_frame.pack(pady=10, fill=tk.X, before=self.results_frame)
    
    def _submit_direct_gesture(self):
        """Submit the directly selected gesture"""
        gesture = self.direct_gesture.get()
        
        if gesture == NO_GESTURE:
            self.results_text.insert(tk.END, "No gesture selected.\n")
            
            # Create a result file
            result = {
                "success": False,
                "gesture": None,
                "timestamp": datetime.now().isoformat()
            }
            
            # Save the result to a file
            with open("gesture_result.json", "w") as f:
                json.dump(result, f)
            
            self.results_text.insert(tk.END, "Created result file with no gesture detected.\n")
            
        else:
            self.results_text.insert(tk.END, f"Selected gesture: {gesture}\n")
            
            # Create a result file
            amount = 200 if gesture == PEACE_SIGN else 330
            result = {
                "success": True,
                "gesture": gesture,
                "timestamp": datetime.now().isoformat(),
                "amount": amount
            }
            
            # Save the result to a file
            with open("gesture_result.json", "w") as f:
                json.dump(result, f)
            
            self.results_text.insert(tk.END, f"Created result file with gesture: {gesture}, amount: {amount} ml\n")
        
        # Hide direct selection frame
        self.direct_selection_frame.pack_forget()

def main():
    """Main function"""
    root = tk.Tk()
    app = GestureTestApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
