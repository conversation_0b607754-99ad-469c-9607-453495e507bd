from flask import Flask, render_template, redirect, url_for, flash, request, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>, UserMixin, login_user, login_required, logout_user, current_user
from werkzeug.security import generate_password_hash, check_password_hash
import os
import secrets
import json
import shutil
import random
from datetime import datetime, timedelta, timezone
from werkzeug.utils import secure_filename

# Import custom modules with error handling
try:
    from water_tracker.image_processing import ImageProcessor
    image_processing_available = True
except ImportError:
    try:
        from image_processing import ImageProcessor
        image_processing_available = True
    except ImportError:
        print("Warning: Image processing module not available. Container recognition feature will be disabled.")
        image_processing_available = False

try:
    from water_tracker.ocr import OCRProcessor
    ocr_available = True
except ImportError:
    try:
        from ocr import OCRProcessor
        ocr_available = True
    except ImportError:
        print("Warning: OCR module not available. Label reading feature will be disabled.")
        ocr_available = False

try:
    from water_tracker.voice_recognition import VoiceProcessor
    voice_recognition_available = True
except ImportError:
    try:
        from voice_recognition import VoiceProcessor
        voice_recognition_available = True
    except ImportError:
        print("Warning: Voice recognition module not available. Voice input feature will be disabled.")
        voice_recognition_available = False

# Try to import gesture recognition
try:
    from water_tracker.gesture_recognition import GestureDetector, mediapipe_available
    gesture_recognition_available = True
    print("Gesture recognition module imported from water_tracker")
except ImportError:
    try:
        from gesture_recognition import GestureDetector, mediapipe_available
        gesture_recognition_available = True
        print("Gesture recognition module imported from local path")
    except ImportError:
        print("Warning: Gesture recognition module not available. Using simple gesture logger.")
        # Force enable gesture recognition regardless of module availability
        gesture_recognition_available = True

# Create Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = secrets.token_hex(16)  # Generate a secure random key
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///water_tracker.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['WTF_CSRF_ENABLED'] = True  # Enable CSRF protection

# OpenWeatherMap API key (get one from https://openweathermap.org/api)
app.config['OPENWEATHERMAP_API_KEY'] = os.environ.get('OPENWEATHERMAP_API_KEY', '********************************')

# File upload configuration
app.config['UPLOAD_FOLDER'] = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'static/uploads')
app.config['CONTAINER_IMAGES'] = os.path.join(app.config['UPLOAD_FOLDER'], 'containers')
app.config['AVATAR_IMAGES'] = os.path.join(app.config['UPLOAD_FOLDER'], 'avatars')
app.config['AUDIO_FILES'] = os.path.join(app.config['UPLOAD_FOLDER'], 'audio')
app.config['ALLOWED_EXTENSIONS'] = {'png', 'jpg', 'jpeg', 'webm', 'mp3', 'wav', 'ogg', 'm4a'}
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max upload size

# Create images directory for n8n webhook
images_dir = os.path.join(os.getcwd(), 'images')
os.makedirs(images_dir, exist_ok=True)

# Serve images directory
from flask import send_from_directory

@app.route('/images/<path:filename>')
def serve_image(filename):
    return send_from_directory(images_dir, filename)

# Initialize database
db = SQLAlchemy(app)

# Initialize login manager
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

# Set up paths for external dependencies
tesseract_paths = [
    r'C:\Program Files\Tesseract-OCR\tesseract.exe',
    r'C:\Program Files (x86)\Tesseract-OCR\tesseract.exe',
    r'C:\Users\<USER>\AppData\Local\Programs\Tesseract-OCR\tesseract.exe',
    r'C:\Users\<USER>\Tesseract-OCR\tesseract.exe'
]

tesseract_path = None
for path in tesseract_paths:
    if os.path.exists(path):
        tesseract_path = path
        print(f"Found Tesseract at: {path}")
        break

# Set up paths for LLaMA model
llama_model_paths = [
    "meta-llama/Llama-2-7b-chat-hf",
    os.path.expanduser("~/llama-models/llama-2-7b-chat"),
    os.path.expanduser("~/models/llama-2-7b-chat"),
    "C:\\llama-models\\llama-2-7b-chat",
    "TheBloke/Llama-2-7B-Chat-GGUF",
    "llama-2-7b-chat.Q4_K_M.gguf"
]

llama_model_path = None
# We don't check if the model exists here since it might be a Hugging Face model ID

# Initialize processors if available
if image_processing_available:
    image_processor = ImageProcessor(app_config=app.config)
else:
    # Force enable image processing with our simplified version
    from water_tracker.image_processing import ImageProcessor
    image_processor = ImageProcessor(app_config=app.config)
    image_processing_available = True
    print("Enabled simplified image processing")

# Try to use the first available LLaMA model path
if llama_model_paths:
    llama_model_path = llama_model_paths[0]

# Initialize OCR processor
try:
    if ocr_available:
        ocr_processor = OCRProcessor(tesseract_path=tesseract_path, llama_model_path=llama_model_path)
    else:
        # Force enable OCR with our simplified version
        from water_tracker.ocr import OCRProcessor
        ocr_processor = OCRProcessor(tesseract_path=tesseract_path, llama_model_path=llama_model_path)
        ocr_available = True
except Exception:
    # If OCR initialization fails, create a minimal OCR processor
    from water_tracker.ocr import OCRProcessor
    ocr_processor = OCRProcessor(tesseract_path=None, llama_model_path=None)
    ocr_available = True

if voice_recognition_available:
    voice_processor = VoiceProcessor()
else:
    # Force enable voice recognition with our simplified version
    from water_tracker.voice_recognition import VoiceProcessor
    voice_processor = VoiceProcessor()
    voice_recognition_available = True
    print("Enabled simplified voice recognition")

# Initialize gesture detector if available
# We'll use a simple gesture detector that works without OpenCV or MediaPipe
class SimpleGestureDetector:
    """Simple gesture detector that launches the standalone gesture logger"""

    def process_image(self, image_path=None):
        """Process a gesture by launching the standalone gesture logger"""
        import subprocess
        import json
        import os
        import sys

        try:
            # First, check if we have the gesture simulator
            if os.path.exists("gesture_simulator.py"):
                # Run the gesture simulator
                print("Launching gesture simulator...")
                result = subprocess.run(
                    [sys.executable, "gesture_simulator.py"],
                    capture_output=True,
                    text=True
                )

                print(f"Gesture simulator output: {result.stdout}")

                # Check if the result file exists
                if os.path.exists("gesture_result.json"):
                    try:
                        # Read the result file
                        with open("gesture_result.json", "r") as f:
                            result_data = json.load(f)

                        # Check if the result was successful
                        if result_data.get('success', False):
                            # Return the result
                            return {
                                'success': True,
                                'gesture': result_data.get('gesture'),
                                'confidence': 0.9,
                                'amount': result_data.get('amount', 200),
                                'annotated_image': "gesture_image.jpg" if os.path.exists("gesture_image.jpg") else None
                            }
                    except Exception as e:
                        print(f"Error reading result file: {e}")

                # If we couldn't get a result from the file, check if we have a direct gesture logger
                if os.path.exists("direct_gesture_logger.py"):
                    # Run the direct gesture logger
                    print("Launching direct gesture logger...")
                    result = subprocess.run(
                        [sys.executable, "direct_gesture_logger.py"],
                        capture_output=True,
                        text=True
                    )

                    print(f"Direct gesture logger output: {result.stdout}")

                    # Process the result
                    return self._process_gesture_result(result.stdout)

            # If we don't have the gesture simulator, check for direct gesture logger
            elif os.path.exists("direct_gesture_logger.py"):
                # Run the direct gesture logger
                print("Launching direct gesture logger...")
                result = subprocess.run(
                    [sys.executable, "direct_gesture_logger.py"],
                    capture_output=True,
                    text=True
                )

                print(f"Direct gesture logger output: {result.stdout}")

                # Process the result
                return self._process_gesture_result(result.stdout)

            # If direct logger doesn't exist, check for working gesture logger
            elif os.path.exists("working_gesture_logger.py"):
                # Run the working gesture logger
                print("Launching working gesture logger...")
                result = subprocess.run(
                    [sys.executable, "working_gesture_logger.py"],
                    capture_output=True,
                    text=True
                )

                print(f"Gesture logger output: {result.stdout}")

                # Process the result
                return self._process_gesture_result(result.stdout)

            # If working logger doesn't exist, check for simple gesture logger
            elif os.path.exists("simple_gesture_logger.py"):
                # Run the simple gesture logger
                print("Launching simple gesture logger...")
                result = subprocess.run(
                    [sys.executable, "simple_gesture_logger.py"],
                    capture_output=True,
                    text=True
                )

                print(f"Gesture logger output: {result.stdout}")

                # Process the result
                return self._process_gesture_result(result.stdout)

            else:
                # No gesture logger found, create a minimal version
                print("No gesture logger found, creating a minimal version")

                # Create a minimal version of the gesture logger
                minimal_logger = """
import tkinter as tk
from tkinter import messagebox
import json
from datetime import datetime
import requests

def log_water_directly(amount, gesture_type):
    try:
        # Create the payload
        data = {
            'amount': amount,
            'drink_type_id': 1,  # Water
            'notes': f'Logged via gesture: {gesture_type}'
        }

        # Send the request to the app
        response = requests.post('http://127.0.0.1:8080/log_water', data=data)

        # Check if the request was successful
        if response.status_code == 200:
            return True, "Water logged successfully!"
        else:
            return False, f"Error logging water: {response.status_code}"
    except Exception as e:
        return False, f"Error logging water: {str(e)}"

def main():
    root = tk.Tk()
    root.withdraw()
    result = messagebox.askyesno(
        "Gesture Detection",
        "Would you like to log 200ml of water with a peace sign gesture?"
    )
    if result:
        # Create the result data
        result_data = {
            "success": True,
            "gesture": "peace_sign",
            "timestamp": datetime.now().isoformat(),
            "amount": 200
        }

        # Save the result to a file
        with open("gesture_result.json", "w") as f:
            json.dump(result_data, f)

        # Try to log water directly
        try:
            success, message = log_water_directly(200, "peace_sign")
            if success:
                messagebox.showinfo("Success", "Successfully logged 200ml of water!")
        except:
            pass

        print("GESTURE_DETECTED:peace_sign")
        return True
    else:
        # Create the result data
        result_data = {
            "success": False,
            "gesture": None,
            "timestamp": datetime.now().isoformat()
        }

        # Save the result to a file
        with open("gesture_result.json", "w") as f:
            json.dump(result_data, f)

        print("NO_GESTURE_DETECTED")
        return False

if __name__ == "__main__":
    main()
                """

                # Save the minimal logger
                with open("minimal_gesture_logger.py", "w") as f:
                    f.write(minimal_logger)

                # Run the minimal logger
                result = subprocess.run(
                    [sys.executable, "minimal_gesture_logger.py"],
                    capture_output=True,
                    text=True
                )

                print(f"Minimal gesture logger output: {result.stdout}")

                # Process the result
                return self._process_gesture_result(result.stdout)

        except Exception as e:
            print(f"Error in simple gesture detector: {e}")
            import traceback
            traceback.print_exc()

            # Return a failure response on error
            return {
                'success': False,
                'error': f"Error processing gesture: {str(e)}",
                'gesture': None,
                'annotated_image': None
            }

    def _process_gesture_result(self, stdout):
        """Process the result from the gesture logger"""
        import json
        import os

        # Check if a gesture was detected from stdout
        if "GESTURE_DETECTED" in stdout:
            # Parse the gesture from the output
            gesture_line = [line for line in stdout.split('\n') if "GESTURE_DETECTED" in line][0]
            gesture = gesture_line.split(':')[1].strip()

            # Check if the result file exists
            if os.path.exists("gesture_result.json"):
                try:
                    # Read the result file
                    with open("gesture_result.json", "r") as f:
                        result_data = json.load(f)

                    # Check if the result was successful
                    if result_data.get('success', False):
                        # Return the result
                        return {
                            'success': True,
                            'gesture': result_data.get('gesture', gesture),
                            'confidence': 0.9,
                            'amount': result_data.get('amount', 200),
                            'annotated_image': None
                        }
                except Exception as e:
                    print(f"Error reading result file: {e}")

            # Default values if result file doesn't exist or wasn't successful
            amount = 200 if gesture == "peace_sign" else 330
            return {
                'success': True,
                'gesture': gesture,
                'confidence': 0.9,
                'amount': amount,
                'annotated_image': None
            }
        else:
            # Check if the result file exists (for additional info)
            if os.path.exists("gesture_result.json"):
                try:
                    with open("gesture_result.json", "r") as f:
                        result_data = json.load(f)

                    # If the file indicates success but stdout didn't, use the file data
                    if result_data.get('success', False) and result_data.get('gesture'):
                        return {
                            'success': True,
                            'gesture': result_data.get('gesture'),
                            'confidence': 0.9,
                            'amount': result_data.get('amount', 200),
                            'annotated_image': None
                        }
                except Exception as e:
                    print(f"Error reading result file: {e}")

            # No gesture detected
            return {
                'success': False,
                'error': "No gesture detected",
                'gesture': None,
                'annotated_image': None
            }

# Create the gesture detector instance
gesture_detector = SimpleGestureDetector()
print("Simple gesture detector initialized")

# Define DrinkType model
class DrinkType(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False)
    hydration_factor = db.Column(db.Float, default=1.0)  # Water = 1.0, others might be less
    color = db.Column(db.String(20), default="#4DA6FF")  # Default blue for water
    icon = db.Column(db.String(50), nullable=True)  # Icon identifier
    water_logs = db.relationship('WaterLog', backref='drink_type', lazy=True)

# Define Container model
class Container(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    volume = db.Column(db.Integer, nullable=False)  # in ml
    image_path = db.Column(db.String(255), nullable=True)
    features = db.Column(db.Text, nullable=True)  # JSON string of container features for recognition
    created_at = db.Column(db.DateTime, default=datetime.now)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    drink_type_id = db.Column(db.Integer, db.ForeignKey('drink_type.id'), nullable=True)
    water_logs = db.relationship('WaterLog', backref='container', lazy=True)

# Define User model
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(100), unique=True, nullable=False)
    email = db.Column(db.String(100), unique=True, nullable=False)
    password_hash = db.Column(db.String(200), nullable=False)
    daily_goal = db.Column(db.Integer, default=2000)
    preferred_unit = db.Column(db.String(10), default="ml")
    theme = db.Column(db.String(10), default="light")
    accent_color = db.Column(db.String(20), default="blue")
    reminder_enabled = db.Column(db.Boolean, default=False)
    reminder_interval = db.Column(db.Integer, default=60)  # minutes
    join_date = db.Column(db.DateTime, default=datetime.now)
    gender = db.Column(db.String(20), nullable=True)  # 'male', 'female', 'custom'
    avatar_path = db.Column(db.String(255), nullable=True)
    water_logs = db.relationship('WaterLog', backref='user', lazy=True)
    containers = db.relationship('Container', backref='user', lazy=True)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

# Define WaterLog model
class WaterLog(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    amount = db.Column(db.Integer, nullable=False)
    timestamp = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    drink_type_id = db.Column(db.Integer, db.ForeignKey('drink_type.id'), nullable=True)
    container_id = db.Column(db.Integer, db.ForeignKey('container.id'), nullable=True)
    input_method = db.Column(db.String(20), default="manual")  # 'manual', 'image', 'ocr', 'voice'
    notes = db.Column(db.Text, nullable=True)

@login_manager.user_loader
def load_user(user_id):
    return db.session.get(User, int(user_id))

# Routes
@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return render_template('index.html')

@app.route('/register', methods=['GET', 'POST'])
def register():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        username = request.form.get('username')
        email = request.form.get('email')
        password = request.form.get('password')
        confirm_password = request.form.get('confirm_password')
        gender = request.form.get('gender', 'not_specified')
        selected_avatar = request.form.get('selected_avatar')

        # Validation
        if not username or not email or not password:
            flash('All fields are required', 'danger')
            return render_template('register.html')

        if password != confirm_password:
            flash('Passwords do not match', 'danger')
            return render_template('register.html')

        if User.query.filter_by(username=username).first():
            flash('Username already exists', 'danger')
            return render_template('register.html')

        if User.query.filter_by(email=email).first():
            flash('Email already registered', 'danger')
            return render_template('register.html')

        # Create new user with gender
        new_user = User(
            username=username,
            email=email,
            gender=gender
        )
        new_user.set_password(password)

        # Handle avatar upload or selection
        avatar_path = None

        # Check if user uploaded a custom avatar
        if 'avatar' in request.files and request.files['avatar'].filename != '':
            avatar_file = request.files['avatar']
            avatar_path = save_uploaded_file(avatar_file, app.config['AVATAR_IMAGES'])
        # Otherwise use selected avatar if one was chosen
        elif selected_avatar:
            # Make sure the avatars directory exists
            avatars_dir = os.path.join(app.static_folder, 'images', 'avatars')
            os.makedirs(avatars_dir, exist_ok=True)

            # Set the path to the selected avatar
            avatar_path = f'images/avatars/{selected_avatar}'

        # Set avatar path if we have one
        if avatar_path:
            new_user.avatar_path = avatar_path

        db.session.add(new_user)
        db.session.commit()

        flash('Registration successful! Please log in.', 'success')
        return redirect(url_for('login'))

    return render_template('register.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        # Check for demo login
        if username == "demo" and password == "demo123":
            # Find or create demo user
            demo_user = db.session.execute(db.select(User).filter_by(username="demo")).scalar_one_or_none()
            if not demo_user:
                # Create demo user with all required fields
                create_demo_user()
                demo_user = db.session.execute(db.select(User).filter_by(username="demo")).scalar_one_or_none()

            login_user(demo_user)
            flash('Logged in as demo user!', 'success')
            return redirect(url_for('dashboard'))

        # Regular login
        user = db.session.execute(db.select(User).filter_by(username=username)).scalar_one_or_none()

        if user and user.check_password(password):
            login_user(user)
            next_page = request.args.get('next')
            return redirect(next_page or url_for('dashboard'))
        else:
            flash('Invalid username or password', 'danger')

    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('index'))

@app.route('/dashboard')
@login_required
def dashboard():
    # Get today's total
    today = datetime.now(timezone.utc).date()
    today_logs = WaterLog.query.filter(
        WaterLog.user_id == current_user.id,
        db.func.date(WaterLog.timestamp) == today
    ).all()

    today_total = sum(log.amount for log in today_logs)
    progress = min(today_total / current_user.daily_goal, 1) * 100

    # Get user's containers
    user_containers = Container.query.filter_by(user_id=current_user.id).all()

    # Get all drink types
    drink_types = DrinkType.query.all()

    # Get today's logs by drink type
    drink_type_totals = db.session.query(
        DrinkType.name,
        DrinkType.color,
        db.func.sum(WaterLog.amount).label('total')
    ).join(
        WaterLog, WaterLog.drink_type_id == DrinkType.id
    ).filter(
        WaterLog.user_id == current_user.id,
        db.func.date(WaterLog.timestamp) == today
    ).group_by(
        DrinkType.id
    ).all()

    # Format for chart
    drink_labels = [item[0] for item in drink_type_totals]
    drink_data = [item[2] for item in drink_type_totals]
    drink_colors = [item[1] for item in drink_type_totals]

    # Special handling for milk (add border for better visibility)
    drink_borders = []
    for label in drink_labels:
        if label.lower() == 'milk':
            drink_borders.append('#cccccc')
        else:
            drink_borders.append('transparent')

    return render_template('dashboard.html',
                          today_total=today_total,
                          daily_goal=current_user.daily_goal,
                          progress=progress,
                          containers=user_containers,
                          drink_types=drink_types,
                          drink_labels=drink_labels,
                          drink_data=drink_data,
                          drink_colors=drink_colors,
                          drink_borders=drink_borders,
                          image_processing_available=image_processing_available,
                          ocr_available=ocr_available,
                          voice_recognition_available=voice_recognition_available,
                          gesture_recognition_available=gesture_recognition_available,
                          show_weather_widget=True)

@app.route('/dashboard_test')
@login_required
def dashboard_test():
    # Get today's total
    today = datetime.now(timezone.utc).date()
    today_logs = WaterLog.query.filter(
        WaterLog.user_id == current_user.id,
        db.func.date(WaterLog.timestamp) == today
    ).all()

    today_total = sum(log.amount for log in today_logs)
    progress = min(today_total / current_user.daily_goal, 1) * 100

    # Get user's containers
    user_containers = Container.query.filter_by(user_id=current_user.id).all()

    # Get all drink types
    drink_types = DrinkType.query.all()

    # Get today's logs by drink type
    drink_type_totals = db.session.query(
        DrinkType.name,
        DrinkType.color,
        db.func.sum(WaterLog.amount).label('total')
    ).join(
        WaterLog, WaterLog.drink_type_id == DrinkType.id
    ).filter(
        WaterLog.user_id == current_user.id,
        db.func.date(WaterLog.timestamp) == today
    ).group_by(
        DrinkType.id
    ).all()

    # Format for chart
    drink_labels = [item[0] for item in drink_type_totals]
    drink_data = [item[2] for item in drink_type_totals]
    drink_colors = [item[1] for item in drink_type_totals]

    # Special handling for milk (add border for better visibility)
    drink_borders = []
    for label in drink_labels:
        if label.lower() == 'milk':
            drink_borders.append('#cccccc')
        else:
            drink_borders.append('transparent')

    return render_template('dashboard_test.html',
                          today_total=today_total,
                          daily_goal=current_user.daily_goal,
                          progress=progress,
                          containers=user_containers,
                          drink_types=drink_types,
                          drink_labels=drink_labels,
                          drink_data=drink_data,
                          drink_colors=drink_colors,
                          drink_borders=drink_borders,
                          image_processing_available=image_processing_available,
                          ocr_available=ocr_available,
                          voice_recognition_available=voice_recognition_available,
                          show_weather_widget=True)

@app.route('/log_water', methods=['POST'])
@login_required
def log_water():
    amount = request.form.get('amount', type=int)
    drink_type_id = request.form.get('drink_type_id', type=int)
    notes = request.form.get('notes')

    if not amount or amount <= 0:
        flash('Please enter a valid amount', 'danger')
        return redirect(url_for('dashboard'))

    # If no drink type specified, default to water (id=1)
    if not drink_type_id:
        water_type = DrinkType.query.filter_by(name='Water').first()
        drink_type_id = water_type.id if water_type else None

    new_log = WaterLog(
        amount=amount,
        user_id=current_user.id,
        drink_type_id=drink_type_id,
        input_method='manual',
        notes=notes
    )

    db.session.add(new_log)
    db.session.commit()

    # Get the drink type name for the flash message
    drink_name = "water"
    if drink_type_id:
        drink_type = DrinkType.query.get(drink_type_id)
        if drink_type:
            drink_name = drink_type.name.lower()

    flash(f'{amount} ml of {drink_name} logged successfully!', 'success')
    return redirect(url_for('dashboard'))

@app.route('/chart')
@login_required
def chart():
    # Get last 7 days data
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=6)

    # Query data for last 7 days
    results = db.session.query(
        db.func.date(WaterLog.timestamp).label('day'),
        db.func.sum(WaterLog.amount).label('total')
    ).filter(
        WaterLog.user_id == current_user.id,
        db.func.date(WaterLog.timestamp) >= start_date,
        db.func.date(WaterLog.timestamp) <= end_date
    ).group_by(
        db.func.date(WaterLog.timestamp)
    ).order_by(
        db.func.date(WaterLog.timestamp)
    ).all()

    # Format dates and prepare data for chart
    dates = []
    amounts = []

    for day, total in results:
        # Check if day is already a string
        if isinstance(day, str):
            day_str = day
        else:
            day_str = day.strftime('%Y-%m-%d')

        dates.append(day_str)
        amounts.append(total)

    # Get data for weekly comparison
    # This week
    this_week_data = amounts

    # Last week
    last_week_end = start_date - timedelta(days=1)
    last_week_start = last_week_end - timedelta(days=6)

    last_week_results = db.session.query(
        db.func.date(WaterLog.timestamp).label('day'),
        db.func.sum(WaterLog.amount).label('total')
    ).filter(
        WaterLog.user_id == current_user.id,
        db.func.date(WaterLog.timestamp) >= last_week_start,
        db.func.date(WaterLog.timestamp) <= last_week_end
    ).group_by(
        db.func.date(WaterLog.timestamp)
    ).order_by(
        db.func.date(WaterLog.timestamp)
    ).all()

    last_week_amounts = [0] * 7
    last_week_date_to_index = {(last_week_start + timedelta(days=i)).strftime('%Y-%m-%d'): i for i in range(7)}

    for day, total in last_week_results:
        # Check if day is already a string
        if isinstance(day, str):
            day_str = day
        else:
            day_str = day.strftime('%Y-%m-%d')

        if day_str in last_week_date_to_index:
            last_week_amounts[last_week_date_to_index[day_str]] = total

    # Prepare chart data
    chart_data = {
        'dates': dates,
        'amounts': amounts,
        'thisWeek': this_week_data,
        'lastWeek': last_week_amounts
    }

    return render_template('chart.html', chart_data=chart_data)

@app.route('/profile')
@login_required
def profile():
    # Get user stats
    total_logs = WaterLog.query.filter_by(user_id=current_user.id).count()

    # Get total amount
    total_amount = db.session.query(db.func.sum(WaterLog.amount)).filter_by(user_id=current_user.id).scalar() or 0

    # Get streak (consecutive days with logs)
    streak = 0
    today = datetime.now().date()

    for i in range(30):  # Check up to 30 days back
        check_date = today - timedelta(days=i)
        logs_on_date = WaterLog.query.filter(
            WaterLog.user_id == current_user.id,
            db.func.date(WaterLog.timestamp) == check_date
        ).first()

        if logs_on_date:
            if i == 0 or streak > 0:  # Today counts or we're already on a streak
                streak += 1
        elif streak > 0:  # Break the streak if a day is missed
            break

    # Get best day (day with highest intake)
    best_day_result = db.session.query(
        db.func.date(WaterLog.timestamp).label('day'),
        db.func.sum(WaterLog.amount).label('total')
    ).filter_by(user_id=current_user.id).group_by('day').order_by(db.desc('total')).first()

    # Initialize with default values
    best_day = {
        'date': None,
        'amount': 0
    }

    # Only try to access result if it exists
    if best_day_result:
        # Get the amount
        best_day['amount'] = best_day_result[1]

        # Handle the date - could be a datetime or a string
        date_value = best_day_result[0]
        if date_value:
            # Try to format it if it's a datetime
            try:
                if hasattr(date_value, 'strftime'):
                    best_day['date'] = date_value.strftime('%B %d, %Y')
                else:
                    # It's probably already a string
                    best_day['date'] = str(date_value)
            except Exception:
                # Fallback to string representation
                best_day['date'] = str(date_value)

    # Format join date
    join_date = current_user.join_date
    formatted_join_date = None

    if join_date:
        try:
            if hasattr(join_date, 'strftime'):
                formatted_join_date = join_date.strftime('%B %d, %Y')
            else:
                # It's probably already a string
                formatted_join_date = str(join_date)
        except Exception:
            # Fallback to string representation
            formatted_join_date = str(join_date)

    return render_template('profile.html',
                          total_logs=total_logs,
                          total_amount=total_amount,
                          streak=streak,
                          best_day=best_day,
                          join_date=formatted_join_date)

@app.route('/settings', methods=['GET', 'POST'])
@login_required
def settings():
    if request.method == 'POST':
        # Get form data
        daily_goal = request.form.get('daily_goal', type=int)
        preferred_unit = request.form.get('preferred_unit')
        theme = request.form.get('theme')
        accent_color = request.form.get('accent_color')
        reminder_enabled = 'reminder_enabled' in request.form
        reminder_interval = request.form.get('reminder_interval', type=int)
        gender = request.form.get('gender')

        # Validate data
        if not daily_goal or daily_goal <= 0:
            flash('Please enter a valid daily goal', 'danger')
            return redirect(url_for('settings'))

        # Update user settings
        current_user.daily_goal = daily_goal

        if preferred_unit in ['ml', 'oz', 'cups']:
            current_user.preferred_unit = preferred_unit

        if theme in ['light', 'dark']:
            current_user.theme = theme

        if accent_color in ['blue', 'green', 'purple', 'orange', 'red']:
            current_user.accent_color = accent_color

        if gender in ['male', 'female', 'custom', 'not_specified']:
            current_user.gender = gender

        current_user.reminder_enabled = reminder_enabled

        if reminder_interval and reminder_interval > 0:
            current_user.reminder_interval = reminder_interval

        # Handle avatar upload
        if 'avatar' in request.files:
            avatar_file = request.files['avatar']
            if avatar_file.filename != '':
                avatar_path = save_uploaded_file(avatar_file, app.config['AVATAR_IMAGES'])
                if avatar_path:
                    current_user.avatar_path = avatar_path

        db.session.commit()

        flash('Settings updated successfully!', 'success')
        return redirect(url_for('dashboard'))

    return render_template('settings.html')

# Container Management Routes
@app.route('/containers')
@login_required
def containers():
    user_containers = Container.query.filter_by(user_id=current_user.id).all()
    drink_types = DrinkType.query.all()

    # Debug: Print container info
    for container in user_containers:
        drink_type_name = "None"
        if container.drink_type_id:
            drink_type = DrinkType.query.get(container.drink_type_id)
            if drink_type:
                drink_type_name = drink_type.name
        print(f"Container: {container.name}, Volume: {container.volume}, Drink Type ID: {container.drink_type_id}, Drink Type: {drink_type_name}")

    return render_template('containers.html', containers=user_containers, drink_types=drink_types)

@app.route('/containers/add', methods=['GET', 'POST'])
@login_required
def add_container():
    drink_types = DrinkType.query.all()

    if request.method == 'POST':
        name = request.form.get('name')
        volume = request.form.get('volume', type=int)
        drink_type_id = request.form.get('drink_type_id', type=int)

        if not name or not volume or volume <= 0:
            flash('Please enter a valid name and volume', 'danger')
            return redirect(url_for('add_container'))

        # Create new container
        new_container = Container(
            name=name,
            volume=volume,
            user_id=current_user.id,
            drink_type_id=drink_type_id
        )

        # Handle image upload
        if 'container_image' in request.files:
            image_file = request.files['container_image']
            if image_file.filename != '':
                image_path = save_uploaded_file(image_file, app.config['CONTAINER_IMAGES'])
                if image_path:
                    new_container.image_path = image_path

        db.session.add(new_container)
        db.session.commit()

        flash('Container added successfully!', 'success')
        return redirect(url_for('containers'))

    return render_template('add_container.html', drink_types=drink_types)

@app.route('/containers/<int:container_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_container(container_id):
    container = Container.query.filter_by(id=container_id, user_id=current_user.id).first_or_404()
    drink_types = DrinkType.query.all()

    if request.method == 'POST':
        name = request.form.get('name')
        volume = request.form.get('volume', type=int)
        drink_type_id = request.form.get('drink_type_id', type=int)

        if not name or not volume or volume <= 0:
            flash('Please enter a valid name and volume', 'danger')
            return redirect(url_for('edit_container', container_id=container_id))

        # Update container
        container.name = name
        container.volume = volume
        container.drink_type_id = drink_type_id

        # Print debug info
        drink_type_name = "None"
        if drink_type_id:
            drink_type = DrinkType.query.get(drink_type_id)
            if drink_type:
                drink_type_name = drink_type.name

        print(f"Updating container: name={name}, volume={volume}, drink_type_id={drink_type_id}, drink_type={drink_type_name}")

        # Handle image upload
        if 'container_image' in request.files:
            image_file = request.files['container_image']
            if image_file.filename != '':
                image_path = save_uploaded_file(image_file, app.config['CONTAINER_IMAGES'])
                if image_path:
                    container.image_path = image_path

        db.session.commit()

        flash('Container updated successfully!', 'success')
        return redirect(url_for('containers'))

    return render_template('edit_container.html', container=container, drink_types=drink_types)

@app.route('/containers/<int:container_id>/delete', methods=['POST'])
@login_required
def delete_container(container_id):
    container = Container.query.filter_by(id=container_id, user_id=current_user.id).first_or_404()

    try:
        # First, delete any water logs associated with this container
        WaterLog.query.filter_by(container_id=container_id).delete()

        # Then delete the container
        db.session.delete(container)
        db.session.commit()

        # Try to delete the image file if it exists
        if container.image_path:
            try:
                image_path = os.path.join(app.static_folder, container.image_path)
                if os.path.exists(image_path) and not 'pepsi_can.png' in image_path:
                    os.remove(image_path)
                    print(f"Deleted container image: {image_path}")
            except Exception as e:
                print(f"Error deleting container image: {e}")

        flash('Container deleted successfully!', 'success')
    except Exception as e:
        db.session.rollback()
        print(f"Error deleting container: {e}")
        flash('Error deleting container. Please try again.', 'danger')

    return redirect(url_for('containers'))

@app.route('/log_water_with_container/<int:container_id>', methods=['POST'])
@login_required
def log_water_with_container(container_id):
    container = Container.query.filter_by(id=container_id, user_id=current_user.id).first_or_404()
    drink_type_id = request.form.get('drink_type_id', type=int)

    # If no drink type specified, use the container's drink type if available
    if not drink_type_id and container.drink_type_id:
        drink_type_id = container.drink_type_id
        print(f"Using container's drink type: {drink_type_id}")

    # Create new water log
    new_log = WaterLog(
        amount=container.volume,
        user_id=current_user.id,
        container_id=container_id,
        drink_type_id=drink_type_id,
        input_method='container'
    )

    db.session.add(new_log)
    db.session.commit()

    flash(f'{container.volume} ml logged successfully using {container.name}!', 'success')
    return redirect(url_for('dashboard'))

# Smart Object Recognition Routes
@app.route('/recognize_container', methods=['GET', 'POST'])
@login_required
def recognize_container():
    # Check if image processing is available
    if not image_processing_available:
        flash('Container recognition is not available. Please install the required dependencies.', 'danger')
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        # Check if image was uploaded
        if 'container_image' not in request.files:
            flash('No image uploaded', 'danger')
            return redirect(url_for('dashboard'))

        image_file = request.files['container_image']
        if image_file.filename == '':
            flash('No image selected', 'danger')
            return redirect(url_for('dashboard'))

        # Save the uploaded image
        image_path = save_uploaded_file(image_file, app.config['CONTAINER_IMAGES'])
        if not image_path:
            flash('Error saving image', 'danger')
            return redirect(url_for('dashboard'))

        # Get full path to the image
        # Fix the path to avoid double 'uploads' in the path
        if image_path.startswith('uploads/'):
            full_image_path = os.path.join(app.static_folder, image_path)
        else:
            # Make sure we have the correct path structure
            if not os.path.exists(os.path.join(app.config['UPLOAD_FOLDER'], image_path)):
                # Try to fix the path
                if 'containers' in image_path:
                    # This is a container image
                    filename = os.path.basename(image_path)
                    fixed_path = os.path.join('uploads', 'containers', filename)
                    if os.path.exists(os.path.join(app.static_folder, fixed_path)):
                        image_path = fixed_path
                        full_image_path = os.path.join(app.static_folder, fixed_path)
                    else:
                        # Create the directory if it doesn't exist
                        os.makedirs(os.path.join(app.static_folder, 'uploads', 'containers'), exist_ok=True)
                        # Copy the file to the correct location
                        try:
                            shutil.copy(os.path.join(app.config['UPLOAD_FOLDER'], image_path),
                                       os.path.join(app.static_folder, fixed_path))
                            image_path = fixed_path
                            full_image_path = os.path.join(app.static_folder, fixed_path)
                        except:
                            full_image_path = os.path.join(app.config['UPLOAD_FOLDER'], image_path)
                else:
                    full_image_path = os.path.join(app.config['UPLOAD_FOLDER'], image_path)
            else:
                full_image_path = os.path.join(app.config['UPLOAD_FOLDER'], image_path)

        # Get user's containers
        user_containers = Container.query.filter_by(user_id=current_user.id).all()

        # Try to match the image with existing containers
        best_match = None
        best_score = 0

        # For demo purposes, always show the container not recognized page
        # This allows users to see and test the calibration feature
        if 'demo_recognize' in request.form and request.form['demo_recognize'] == 'true':
            # In demo mode, find a random container to recognize
            if user_containers:
                import random
                best_match = random.choice(user_containers)
                best_score = 0.85
        else:
            # Normal matching logic
            for container in user_containers:
                if container.features:
                    is_match, score = image_processor.match_container(full_image_path, container.features)
                    if is_match and score > best_score:
                        best_match = container
                        best_score = score

        if best_match:
            # Get drink types for logging
            drink_types = DrinkType.query.all()
            return render_template('container_recognized.html',
                                  container=best_match,
                                  image_path=image_path,
                                  drink_types=drink_types)
        else:
            # No match found, ask user to calibrate
            # Make sure the image path is correct and the image exists
            print(f"Image path for container not recognized: {image_path}")
            print(f"Full image path: {full_image_path}")
            print(f"Image exists: {os.path.exists(full_image_path)}")

            # Try to detect the drink type from the image
            detected_drink_type = None
            detected_drink_id = None

            # Check if the image exists
            print(f"Checking if image exists at: {full_image_path}")
            if os.path.exists(full_image_path):
                print(f"Image exists, attempting to detect drink type")
                try:
                    # Detect drink type from the image
                    drink_type_name, confidence = image_processor.detect_drink_type(full_image_path)
                    print(f"Detected drink type: {drink_type_name} with confidence {confidence:.2f}")

                    # Find the drink type in the database
                    detected_drink = DrinkType.query.filter_by(name=drink_type_name).first()
                    if detected_drink:
                        detected_drink_type = detected_drink.name
                        detected_drink_id = detected_drink.id
                        print(f"Found drink type in database: {detected_drink_type} (ID: {detected_drink_id})")
                    else:
                        print(f"Could not find drink type '{drink_type_name}' in database")
                        # Try to find a similar drink type
                        for drink_type in DrinkType.query.all():
                            if drink_type_name.lower() in drink_type.name.lower() or drink_type.name.lower() in drink_type_name.lower():
                                detected_drink_type = drink_type.name
                                detected_drink_id = drink_type.id
                                print(f"Found similar drink type: {detected_drink_type} (ID: {detected_drink_id})")
                                break
                except Exception as e:
                    print(f"Error detecting drink type: {e}")
                    import traceback
                    traceback.print_exc()
            else:
                print(f"Image does not exist at: {full_image_path}")

                # Try to detect drink type from filename and full path
                filename = os.path.basename(image_path).lower()
                full_path = image_path.lower()
                print(f"Trying to detect drink type from filename: {filename}")
                print(f"Full path: {full_path}")

                # Check for specific drink types first
                # Water bottle detection takes precedence
                if ('bottle' in filename or 'bottle' in full_path) and ('water' in filename or 'water' in full_path):
                    water_type = DrinkType.query.filter_by(name='Water').first()
                    if water_type:
                        detected_drink_type = water_type.name
                        detected_drink_id = water_type.id
                        print(f"Detected Water Bottle from filename/path (ID: {detected_drink_id})")
                # Tea detection
                elif 'tea' in filename or 'tea' in full_path or ('cup' in filename and 'bottle' not in filename) or ('screenshot' in filename and 'bottle' not in filename):
                    tea_type = DrinkType.query.filter_by(name='Tea').first()
                    if tea_type:
                        detected_drink_type = tea_type.name
                        detected_drink_id = tea_type.id
                        print(f"Detected Tea from filename/path (ID: {detected_drink_id})")
                elif 'juice' in filename or 'juice' in full_path:
                    juice_type = DrinkType.query.filter_by(name='Juice').first()
                    if juice_type:
                        detected_drink_type = juice_type.name
                        detected_drink_id = juice_type.id
                        print(f"Detected Juice from filename/path (ID: {detected_drink_id})")
                elif 'pepsi' in filename or 'pepsi' in full_path:
                    pepsi_type = DrinkType.query.filter_by(name='Pepsi').first()
                    if pepsi_type:
                        detected_drink_type = pepsi_type.name
                        detected_drink_id = pepsi_type.id
                        print(f"Detected Pepsi from filename/path (ID: {detected_drink_id})")
                else:
                    # Check for other drink type keywords in filename
                    for drink_type in DrinkType.query.all():
                        if drink_type.name.lower() in filename:
                            detected_drink_type = drink_type.name
                            detected_drink_id = drink_type.id
                            print(f"Detected drink type from filename: {detected_drink_type} (ID: {detected_drink_id})")
                            break

            # Get all drink types for the form
            drink_types = DrinkType.query.all()

            # Determine volume based on drink type, filename, or defaults
            suggested_volume = 350  # Default

            # First, check if we have a similar container already saved for this user
            similar_container = None

            # Check for similar containers based on filename and detected drink type
            for container in user_containers:
                # If we have a container with the same drink type
                if detected_drink_type and container.drink_type_id:
                    drink_type = DrinkType.query.get(container.drink_type_id)
                    if drink_type and drink_type.name == detected_drink_type:
                        similar_container = container
                        break

                # Or if we have a container with similar name/type in the filename
                if container.name and image_path:
                    container_name_lower = container.name.lower()
                    image_path_lower = image_path.lower()

                    # Check for common terms
                    common_terms = ['bottle', 'cup', 'mug', 'glass', 'can']
                    for term in common_terms:
                        if term in container_name_lower and term in image_path_lower:
                            similar_container = container
                            break

                    # Check for drink types in both
                    for drink_type in drink_types:
                        drink_name_lower = drink_type.name.lower()
                        if drink_name_lower in container_name_lower and drink_name_lower in image_path_lower:
                            similar_container = container
                            break

            # If we found a similar container, use its volume
            if similar_container:
                suggested_volume = similar_container.volume
                print(f"Using volume {suggested_volume} from similar container: {similar_container.name}")
            else:
                # Otherwise, adjust based on detected drink type
                if detected_drink_type:
                    if detected_drink_type == 'Water':
                        # Make sure water bottles are always 500ml
                        if 'bottle' in image_path.lower() or ('bottle' in image_path.lower() and 'water' in image_path.lower()):
                            suggested_volume = 500
                            print(f"Detected water bottle, setting volume to {suggested_volume}ml")
                        else:
                            suggested_volume = 250
                            print(f"Detected water glass, setting volume to {suggested_volume}ml")
                    elif detected_drink_type == 'Coffee':
                        suggested_volume = 200
                    elif detected_drink_type == 'Tea':
                        # Tea cups are typically 250ml
                        suggested_volume = 250
                        print(f"Detected tea cup, setting volume to {suggested_volume}ml")
                        # If it's a screenshot with a tea cup, it's likely 250ml
                        if 'screenshot' in image_path.lower():
                            suggested_volume = 250
                    elif detected_drink_type == 'Milk':
                        suggested_volume = 250
                    elif detected_drink_type == 'Juice':
                        suggested_volume = 300
                    elif detected_drink_type == 'Soda':
                        suggested_volume = 330
                    elif detected_drink_type == 'Pepsi':
                        suggested_volume = 330

                # Further adjust based on container type in filename
                if 'bottle' in image_path.lower():
                    if 'water' in image_path.lower():
                        suggested_volume = 500
                        print(f"Detected water bottle in filename, setting volume to {suggested_volume}ml")
                    else:
                        suggested_volume = max(suggested_volume, 500)
                        print(f"Detected bottle in filename, setting volume to {suggested_volume}ml")
                elif 'can' in image_path.lower():
                    suggested_volume = 330
                    print(f"Detected can in filename, setting volume to {suggested_volume}ml")
                elif 'glass' in image_path.lower():
                    suggested_volume = 250
                    print(f"Detected glass in filename, setting volume to {suggested_volume}ml")
                elif 'cup' in image_path.lower() or 'mug' in image_path.lower() or 'screenshot' in image_path.lower():
                    if 'coffee' in image_path.lower():
                        suggested_volume = 200
                        print(f"Detected coffee cup in filename, setting volume to {suggested_volume}ml")
                    elif 'tea' in image_path.lower() or 'screenshot' in image_path.lower():
                        # Tea cups are typically 250ml
                        suggested_volume = 250
                        print(f"Detected tea cup in filename, setting volume to {suggested_volume}ml")
                    else:
                        suggested_volume = 250
                        print(f"Detected cup/mug in filename, setting volume to {suggested_volume}ml")

            # Suggest a name based on the detected drink type
            suggested_name = ""
            if detected_drink_type:
                if 'can' in image_path.lower():
                    suggested_name = f"{detected_drink_type} Can"
                elif 'bottle' in image_path.lower():
                    suggested_name = f"{detected_drink_type} Bottle"
                else:
                    suggested_name = f"{detected_drink_type} Container"

            return render_template('container_not_recognized.html',
                                  image_path=image_path,
                                  drink_types=drink_types,
                                  detected_drink_id=detected_drink_id,
                                  suggested_name=suggested_name,
                                  suggested_volume=suggested_volume)

    return render_template('recognize_container.html')

@app.route('/calibrate_container', methods=['POST'])
@login_required
def calibrate_container():
    name = request.form.get('name')
    volume = request.form.get('volume', type=int)
    image_path = request.form.get('image_path')
    drink_type_id = request.form.get('drink_type_id', type=int)

    # Print debug information
    drink_type_name = "None"
    if drink_type_id:
        drink_type = DrinkType.query.get(drink_type_id)
        if drink_type:
            drink_type_name = drink_type.name

    print(f"Calibrating container with name: {name}, volume: {volume}, image_path: {image_path}, drink_type_id: {drink_type_id}, drink_type: {drink_type_name}")

    # Handle empty name - use a default name if none provided
    if not name:
        name = "Container " + datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"Using default name: {name}")

    if not volume or volume <= 0:
        volume = 350  # Default to 350ml
        print(f"Using default volume: {volume}")

    if not image_path:
        flash('Error: No image path provided', 'danger')
        return redirect(url_for('recognize_container'))

    # Create new container
    new_container = Container(
        name=name,
        volume=volume,
        image_path=image_path,
        user_id=current_user.id,
        drink_type_id=drink_type_id
    )

    # Extract features from the image
    # Fix the path to avoid double 'uploads' in the path
    if image_path.startswith('uploads/'):
        full_image_path = os.path.join(app.static_folder, image_path)
    else:
        full_image_path = os.path.join(app.config['UPLOAD_FOLDER'], image_path)
    features = image_processor.extract_features(full_image_path)

    if features:
        new_container.features = json.dumps(features)

    db.session.add(new_container)
    db.session.commit()

    flash('Container calibrated successfully!', 'success')
    return redirect(url_for('containers'))

# OCR Label Reading Routes
@app.route('/read_label', methods=['GET', 'POST'])
@login_required
def read_label():
    # Check if OCR is available
    if not ocr_available and request.method == 'GET':
        flash('Label reading is not available. Please install the required dependencies.', 'danger')
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        # Check if image was uploaded
        if 'label_image' not in request.files:
            if request.is_json or request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'error': 'No image uploaded'}), 400
            flash('No image uploaded', 'danger')
            return redirect(url_for('dashboard'))

        image_file = request.files['label_image']
        if image_file.filename == '':
            if request.is_json or request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'error': 'No image selected'}), 400
            flash('No image selected', 'danger')
            return redirect(url_for('dashboard'))

        # Create the images directory if it doesn't exist
        images_dir = os.path.join(os.getcwd(), 'images')
        os.makedirs(images_dir, exist_ok=True)

        # Save the uploaded image to the local images directory
        filename = secure_filename(image_file.filename)
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        filename = f"{timestamp}_{filename}"
        local_image_path = os.path.join(images_dir, filename)
        image_file.save(local_image_path)

        # Store the relative path for the frontend
        relative_local_path = os.path.join('images', filename).replace('\\', '/')

        # Also save to the app's upload folder for display
        image_path = save_uploaded_file(image_file, app.config['UPLOAD_FOLDER'])
        if not image_path:
            if request.is_json or request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'error': 'Error saving image'}), 500
            flash('Error saving image', 'danger')
            return redirect(url_for('dashboard'))

        # Get full path to the image in the static folder
        if image_path.startswith('uploads/'):
            full_static_path = os.path.join(app.static_folder, image_path)
        else:
            full_static_path = os.path.join(app.config['UPLOAD_FOLDER'], image_path)

        # Get the URL for the image
        image_url = url_for('static', filename=image_path)

        # Get full path to the image for OCR processing
        if image_path.startswith('uploads/'):
            full_image_path = os.path.join(app.static_folder, image_path)
        else:
            full_image_path = os.path.join(app.config['UPLOAD_FOLDER'], image_path)

        # Send the image path to the n8n webhook
        import requests
        import re

        # Prepare the payload with just the image path as requested
        webhook_url = "http://localhost:5678/webhook-test/f22cc1d4-3482-465a-97d6-478411c29099"

        # Create the payload with the image path exactly as requested
        payload = {
            "image_path": local_image_path
        }

        print(f"Sending image path to n8n webhook: {local_image_path}")
        print(f"Payload: {payload}")

        try:
            # Send the request to n8n with the image path
            print("Sending request to n8n webhook...")
            response = requests.post(webhook_url, json=payload, timeout=30)
            print(f"n8n webhook response status: {response.status_code}")

            # Check if the response is valid
            if response.status_code != 200:
                print(f"n8n webhook error: {response.text}")
                raise Exception(f"n8n webhook returned status code {response.status_code}")

            # Parse the response from n8n
            n8n_result = response.json()
            print(f"n8n webhook response: {n8n_result}")

            # Ensure we have the expected fields and handle the new format
            print(f"Received n8n response: {n8n_result}")

            # Map the response fields to our expected format
            drink_type = None
            if 'drink-type' in n8n_result:
                drink_type = n8n_result['drink-type']
                n8n_result['drinktype'] = n8n_result['drink-type']
            elif 'drinktype' in n8n_result:
                drink_type = n8n_result['drinktype']
            else:
                print("Warning: No drink type field in n8n response")
                drink_type = 'Water'
                n8n_result['drinktype'] = drink_type

            # Add default values if missing
            if 'volume' not in n8n_result:
                print("Warning: 'volume' field missing in n8n response")
                n8n_result['volume'] = '350 ml'

            # Ensure volume has proper format (number + ml)
            volume_text = n8n_result['volume']
            if not isinstance(volume_text, str):
                volume_text = str(volume_text)

            # Check for duplicate "ml ml" which can happen with OCR errors
            if 'ml ml' in volume_text.lower():
                # Fix the duplicate ml
                volume_text = volume_text.lower().replace('ml ml', 'ml')
                print(f"Fixed duplicate 'ml ml' in text: '{volume_text}'")

            # Special case for Pepsi cans - OCR often misreads 330ml as 33ml
            drink_type = n8n_result.get('drink-type', n8n_result.get('drinktype', '')).lower()
            if 'pepsi' in drink_type:
                # Check if the volume is a small number (likely misread)
                volume_match = re.search(r'(\d+)', volume_text)
                if volume_match:
                    extracted_num = int(volume_match.group(1))
                    if extracted_num > 0 and extracted_num < 100:
                        volume_text = "330 ml"
                        print(f"Detected Pepsi can with small volume ({extracted_num}ml), correcting to 330ml")

            # Check for "1 ml" which might actually be "1L" misread by OCR
            if re.search(r'^\s*1\s*ml\s*$', volume_text.lower()) and ('juice' in drink_type or 'orange' in drink_type):
                volume_text = "1000 ml"
                print(f"Detected likely '1L' misread as '1 ml', correcting to 1000ml")

            # Special case for orange juice bottles - often 1L but OCR might miss it
            elif 'orange' in drink_type.lower() and 'juice' in drink_type.lower() and (not volume_text or volume_text == '0 ml' or volume_text.lower() == 'juice'):
                volume_text = "1000 ml"
                print(f"Detected orange juice bottle with missing volume, defaulting to 1000ml (1L)")

            # Check for "1L" or similar text that might be misread in the extracted text
            elif 'text' in n8n_result and re.search(r'1\s*[lL]', n8n_result['text']) and ('juice' in drink_type or 'orange' in drink_type):
                volume_text = "1000 ml"
                print(f"Detected '1L' in extracted text for juice, setting to 1000ml")

            # Check for liters in the text (1L, 1l, 1 liter, etc.)
            liter_match = re.search(r'(\d+(?:\.\d+)?)(?:\s*(?:l|L|liter|liters|Liter|Liters))', volume_text)
            if liter_match:
                # Convert liters to milliliters (1L = 1000ml)
                liters = float(liter_match.group(1))
                volume_value = int(liters * 1000)
                volume_text = f"{volume_value} ml"
                print(f"Converted {liters}L to {volume_value}ml from text: '{volume_text}'")
            # Check for "1L" in the image or label text
            elif '1l' in volume_text.lower() or '1 l' in volume_text.lower():
                volume_text = "1000 ml"
                print(f"Detected 1L in text, setting volume to 1000ml from text: '{volume_text}'")
            # Check for "1.5L" in the image or label text
            elif '1.5l' in volume_text.lower() or '1.5 l' in volume_text.lower():
                volume_text = "1500 ml"
                print(f"Detected 1.5L in text, setting volume to 1500ml from text: '{volume_text}'")
            # Check for "2L" in the image or label text
            elif '2l' in volume_text.lower() or '2 l' in volume_text.lower():
                volume_text = "2000 ml"
                print(f"Detected 2L in text, setting volume to 2000ml from text: '{volume_text}'")
            # Add 'ml' if it's not already there
            elif 'ml' not in volume_text.lower():
                # Check if it's just a number
                if volume_text.isdigit():
                    # Check if this might be a liter value that wasn't properly detected
                    extracted_num = int(volume_text)
                    if extracted_num <= 3 and ('juice' in drink_name.lower() or 'water' in drink_name.lower() or 'orange' in drink_name.lower()):
                        # Small numbers (1-3) for juice or water are likely liters
                        volume_value = extracted_num * 1000
                        volume_text = f"{volume_value} ml"
                        print(f"Converted likely liter value {extracted_num}L to {volume_value}ml from text: '{volume_text}'")
                    else:
                        volume_text = f"{volume_text} ml"
                else:
                    # Try to extract a number
                    import re
                    print(f"Raw volume text for formatting: '{volume_text}'")

                    # Try multiple regex patterns to extract the volume
                    # First, try to find a number followed by "ml" (case insensitive)
                    volume_match = re.search(r'(\d+)(?:\s*(?:ml|ML|mL|Ml))', volume_text)
                    if volume_match:
                        volume_value = int(volume_match.group(1))
                        volume_text = f"{volume_value} ml"
                        print(f"Formatted volume text using pattern 1: {volume_text}")
                    else:
                        # Try to find any number in the text
                        volume_match = re.search(r'(\d+)', volume_text)
                        if volume_match:
                            volume_value = int(volume_match.group(1))
                            # Check if this might be a liter value that wasn't properly detected
                            if volume_value <= 3 and ('juice' in drink_name.lower() or 'water' in drink_name.lower() or 'orange' in drink_name.lower()):
                                # Small numbers (1-3) for juice or water are likely liters
                                volume_value = volume_value * 1000
                                volume_text = f"{volume_value} ml"
                                print(f"Converted likely liter value to {volume_value}ml from text: '{volume_text}'")
                            else:
                                volume_text = f"{volume_value} ml"
                                print(f"Formatted volume text using pattern 2: {volume_text}")
                        else:
                            # If "330" is in the text in any form (like "33OML" where O might be misread as 0)
                            if '330' in volume_text.replace('O', '0').replace('o', '0'):
                                volume_text = "330 ml"
                                print(f"Formatted volume text using OCR correction: {volume_text}")
                            # If "500" is in the text in any form
                            elif '500' in volume_text.replace('O', '0').replace('o', '0'):
                                volume_text = "500 ml"
                                print(f"Formatted volume text using OCR correction: {volume_text}")
                            # If "250" is in the text in any form
                            elif '250' in volume_text.replace('O', '0').replace('o', '0'):
                                volume_text = "250 ml"
                                print(f"Formatted volume text using OCR correction: {volume_text}")
                            else:
                                # If we still can't extract a volume, keep the original text
                                print(f"WARNING: Could not format volume text: '{volume_text}'")
                                # Don't set a default value, keep the original text

            n8n_result['volume'] = volume_text

            # Add extracted text field if not present
            if 'text' not in n8n_result:
                # Use drink-type if available, otherwise use drinktype
                drink_type = n8n_result.get('drink-type', n8n_result.get('drinktype', 'Unknown'))
                n8n_result['text'] = f"{drink_type} {n8n_result.get('volume', 'Unknown')}"

            # If this is an AJAX request, return JSON
            if request.is_json or request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                # Use drink-type if available, otherwise use drinktype
                drink_type = n8n_result.get('drink-type', n8n_result.get('drinktype', 'Unknown'))

                return jsonify({
                    'success': True,
                    'volume': n8n_result.get('volume', 'Unknown'),
                    'drinktype': drink_type,
                    'text': n8n_result.get('text', f"{drink_type} {n8n_result.get('volume', 'Unknown')}"),
                    'image_url': image_url,
                    'local_image_path': relative_local_path,
                    'redirect': url_for('label_recognized',
                                       volume=n8n_result.get('volume', 'Unknown'),
                                       drinktype=drink_type,
                                       text=n8n_result.get('text', f"{drink_type} {n8n_result.get('volume', 'Unknown')}"),
                                       image_path=image_path)
                })

            # For traditional form submission, use the n8n result
            # Get drink types for the dropdown
            drink_types = DrinkType.query.all()

            # Try to suggest a drink type based on the n8n result
            suggested_drink_type_id = None

            # Use drink-type if available, otherwise use drinktype
            drink_name = n8n_result.get('drink-type', n8n_result.get('drinktype', '')).lower()

            # Try to find a matching drink type
            for drink_type in drink_types:
                if drink_name in drink_type.name.lower():
                    suggested_drink_type_id = drink_type.id
                    break

            # Use volume from n8n if available
            volume_text = n8n_result.get('volume', '')
            volume = 0

            # Print the raw volume text for debugging
            print(f"Raw volume text from n8n: '{volume_text}'")

            # Check for liters in the text (1L, 1l, 1 liter, etc.)
            liter_match = re.search(r'(\d+(?:\.\d+)?)(?:\s*(?:l|L|liter|liters|Liter|Liters))', volume_text)
            if liter_match:
                # Convert liters to milliliters (1L = 1000ml)
                liters = float(liter_match.group(1))
                volume = int(liters * 1000)
                print(f"Converted {liters}L to {volume}ml from text: '{volume_text}'")
            else:
                # Check for "1L" in the image or label text
                if '1l' in volume_text.lower() or '1 l' in volume_text.lower():
                    volume = 1000
                    print(f"Detected 1L in text, setting volume to 1000ml from text: '{volume_text}'")
                # Check for "1.5L" in the image or label text
                elif '1.5l' in volume_text.lower() or '1.5 l' in volume_text.lower():
                    volume = 1500
                    print(f"Detected 1.5L in text, setting volume to 1500ml from text: '{volume_text}'")
                # Check for "2L" in the image or label text
                elif '2l' in volume_text.lower() or '2 l' in volume_text.lower():
                    volume = 2000
                    print(f"Detected 2L in text, setting volume to 2000ml from text: '{volume_text}'")
                else:
                    # Try to find a number followed by "ml" (case insensitive)
                    volume_match = re.search(r'(\d+)(?:\s*(?:ml|ML|mL|Ml))', volume_text)
                    if volume_match:
                        volume = int(volume_match.group(1))
                        print(f"Extracted volume using pattern 1: {volume} ml from text: '{volume_text}'")
                    else:
                        # Try to find any number in the text
                        volume_match = re.search(r'(\d+)', volume_text)
                        if volume_match:
                            # Check if this might be a liter value that wasn't properly detected
                            extracted_num = int(volume_match.group(1))
                            if extracted_num <= 3 and ('juice' in drink_name.lower() or 'water' in drink_name.lower()):
                                # Small numbers (1-3) for juice or water are likely liters
                                volume = extracted_num * 1000
                                print(f"Converted likely liter value {extracted_num}L to {volume}ml from text: '{volume_text}'")
                            else:
                                volume = extracted_num
                                print(f"Extracted volume using pattern 2: {volume} ml from text: '{volume_text}'")
                        else:
                            # If "330" is in the text in any form (like "33OML" where O might be misread as 0)
                            if '330' in volume_text.replace('O', '0').replace('o', '0'):
                                volume = 330
                                print(f"Extracted volume using OCR correction: 330 ml from text: '{volume_text}'")
                            # If "500" is in the text in any form
                            elif '500' in volume_text.replace('O', '0').replace('o', '0'):
                                volume = 500
                                print(f"Extracted volume using OCR correction: 500 ml from text: '{volume_text}'")
                            # If "250" is in the text in any form
                            elif '250' in volume_text.replace('O', '0').replace('o', '0'):
                                volume = 250
                                print(f"Extracted volume using OCR correction: 250 ml from text: '{volume_text}'")
                            # Special case for orange juice - default to 1L (1000ml)
                            elif 'orange' in drink_type.lower() and 'juice' in drink_type.lower():
                                volume = 1000
                                print(f"Detected orange juice with no volume, defaulting to 1000ml (1L)")
                            else:
                                # If we still can't extract a volume, use the original text
                                print(f"WARNING: Could not extract volume from n8n response: '{volume_text}'")
                                # Ask the user to manually enter the volume
                                volume = 0  # This will trigger the "Invalid volume" error and redirect to dashboard

            # Create text for display
            # Use drink-type if available, otherwise use drinktype
            drink_type = n8n_result.get('drink-type', n8n_result.get('drinktype', 'Unknown'))
            text = f"{drink_type} {n8n_result.get('volume', 'Unknown')}"

            return render_template('label_recognized.html',
                                  volume=volume,
                                  text=text,
                                  image_path=image_path,
                                  drink_types=drink_types,
                                  suggested_drink_type_id=suggested_drink_type_id,
                                  extraction_method='n8n')

        except Exception as e:
            print(f"Error with n8n webhook: {e}")
            import traceback
            traceback.print_exc()

            # If this is an AJAX request, return error JSON
            if request.is_json or request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({
                    'success': False,
                    'error': f"Error with n8n webhook: {str(e)}",
                    'image_url': image_url
                })

            # Fall back to OCR processing for traditional form submission
            print("Falling back to OCR processing due to n8n webhook error")
            try:
                result = ocr_processor.process_label(full_image_path)
            except Exception as ocr_error:
                print(f"Error with OCR processing: {ocr_error}")
                traceback.print_exc()
                # Return a generic error response
                if request.is_json or request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return jsonify({
                        'success': False,
                        'error': f"Error processing image: {str(ocr_error)}",
                        'image_url': image_url
                    })
                return render_template('label_not_recognized.html',
                                      text=f"Error: {str(ocr_error)}",
                                      image_path=image_path,
                                      drink_types=DrinkType.query.all())

            # Process the OCR result
            if result['success']:
                drink_types = DrinkType.query.all()
                suggested_drink_type_id = None
                extraction_method = result.get('method', 'ocr')

                # Try to suggest a drink type based on the OCR result
                if 'drink_type' in result and result['drink_type']:
                    drink_name = result['drink_type'].lower()
                    for drink_type in drink_types:
                        if drink_name in drink_type.name.lower():
                            suggested_drink_type_id = drink_type.id
                            break

                return render_template('label_recognized.html',
                                      volume=result['volume_ml'],
                                      text=result['text'],
                                      image_path=image_path,
                                      drink_types=drink_types,
                                      suggested_drink_type_id=suggested_drink_type_id,
                                      extraction_method=extraction_method)
            else:
                drink_types = DrinkType.query.all()
                return render_template('label_not_recognized.html',
                                      text=result.get('text', ''),
                                      image_path=image_path,
                                      drink_types=drink_types)





    return render_template('read_label.html')

@app.route('/label_recognized', methods=['GET'])
@login_required
def label_recognized():
    # Get parameters from the URL
    volume = request.args.get('volume', '0')
    drinktype = request.args.get('drinktype', 'Unknown')
    text = request.args.get('text', '')
    image_path = request.args.get('image_path', '')

    print(f"label_recognized route received volume: {volume}, type: {type(volume)}")

    # Extract numeric value from volume text (e.g., "350ml" -> 350)
    import re
    print(f"Raw volume text in label_recognized: '{volume}'")

    # Check for duplicate "ml ml" which can happen with OCR errors
    if isinstance(volume, str) and 'ml ml' in volume.lower():
        # Fix the duplicate ml
        volume = volume.lower().replace('ml ml', 'ml')
        print(f"Fixed duplicate 'ml ml' in text: '{volume}'")

    # Check for "1 ml" which might actually be "1L" misread by OCR
    if isinstance(volume, str) and re.search(r'^\s*1\s*ml\s*$', volume.lower()) and ('juice' in text.lower() or 'orange' in text.lower()):
        volume = 1000
        print(f"Detected likely '1L' misread as '1 ml', correcting to 1000ml")

    # Check for liters in the text (1L, 1l, 1 liter, etc.)
    if isinstance(volume, str):
        liter_match = re.search(r'(\d+(?:\.\d+)?)(?:\s*(?:l|L|liter|liters|Liter|Liters))', volume)
        if liter_match:
            # Convert liters to milliliters (1L = 1000ml)
            liters = float(liter_match.group(1))
            volume = int(liters * 1000)
            print(f"Converted {liters}L to {volume}ml from text: '{volume}'")
        else:
            # Check for "1L" in the image or label text
            if '1l' in volume.lower() or '1 l' in volume.lower():
                volume = 1000
                print(f"Detected 1L in text, setting volume to 1000ml from text: '{volume}'")
            # Check for "1.5L" in the image or label text
            elif '1.5l' in volume.lower() or '1.5 l' in volume.lower():
                volume = 1500
                print(f"Detected 1.5L in text, setting volume to 1500ml from text: '{volume}'")
            # Check for "2L" in the image or label text
            elif '2l' in volume.lower() or '2 l' in volume.lower():
                volume = 2000
                print(f"Detected 2L in text, setting volume to 2000ml from text: '{volume}'")
            else:
                # Try to find a number followed by "ml" (case insensitive)
                volume_match = re.search(r'(\d+)(?:\s*(?:ml|ML|mL|Ml))', volume)
                if volume_match:
                    volume = int(volume_match.group(1))
                    print(f"Extracted volume using pattern 1: {volume} ml from text: '{volume}'")
                else:
                    # Try to find any number in the text
                    volume_match = re.search(r'(\d+)', volume)
                    if volume_match:
                        # Check if this might be a liter value that wasn't properly detected
                        extracted_num = int(volume_match.group(1))
                        if extracted_num <= 3 and ('juice' in text.lower() or 'water' in text.lower() or 'orange' in text.lower()):
                            # Small numbers (1-3) for juice or water are likely liters
                            volume = extracted_num * 1000
                            print(f"Converted likely liter value {extracted_num}L to {volume}ml from text: '{volume}'")
                        else:
                            volume = extracted_num
                            print(f"Extracted volume using pattern 2: {volume} ml from text: '{volume}'")
                    else:
                        # If "330" is in the text in any form (like "33OML" where O might be misread as 0)
                        if '330' in volume.replace('O', '0').replace('o', '0'):
                            volume = 330
                            print(f"Extracted volume using OCR correction: 330 ml from text: '{volume}'")
                        # If "500" is in the text in any form
                        elif '500' in volume.replace('O', '0').replace('o', '0'):
                            volume = 500
                            print(f"Extracted volume using OCR correction: 500 ml from text: '{volume}'")
                        # If "250" is in the text in any form
                        elif '250' in volume.replace('O', '0').replace('o', '0'):
                            volume = 250
                            print(f"Extracted volume using OCR correction: 250 ml from text: '{volume}'")
                        # Special case for orange juice - default to 1L (1000ml)
                        elif 'orange' in text.lower() and 'juice' in text.lower():
                            volume = 1000
                            print(f"Detected orange juice with no volume, defaulting to 1000ml (1L)")
                        else:
                            # If we still can't extract a volume, show an error
                            print(f"WARNING: Could not extract volume from text: '{volume}'")
                            volume = 0  # This will trigger the "Invalid volume" error

    # Get drink types for the dropdown
    drink_types = DrinkType.query.all()

    # Try to suggest a drink type based on the drinktype
    suggested_drink_type_id = None
    drink_name = drinktype.lower()

    # Special case for orange juice with missing volume
    if ('orange' in drink_name or 'juice' in drink_name) and (not volume or volume == 0 or volume == '0'):
        print(f"Detected Orange Juice with missing volume in label_recognized route, setting to 1000ml")
        volume = 1000

    # Try to find a matching drink type
    for drink_type in drink_types:
        if drink_name in drink_type.name.lower():
            suggested_drink_type_id = drink_type.id
            break

    return render_template('label_recognized.html',
                          volume=volume,
                          text=text,
                          image_path=image_path,
                          drink_types=drink_types,
                          suggested_drink_type_id=suggested_drink_type_id,
                          extraction_method='n8n')

@app.route('/log_from_label', methods=['POST'])
@login_required
def log_from_label():
    # Get the raw volume value from the form
    raw_volume = request.form.get('volume')
    print(f"log_from_label received raw volume: {raw_volume}, type: {type(raw_volume)}")

    # Extract numeric value from the volume string if it's not already a number
    if raw_volume and isinstance(raw_volume, str):
        import re
        # Extract only the numeric part
        numeric_match = re.search(r'(\d+)', raw_volume)
        if numeric_match:
            raw_volume = numeric_match.group(1)
            print(f"Extracted numeric volume: {raw_volume}")

    # Convert to integer
    volume = int(raw_volume) if raw_volume and raw_volume.isdigit() else 0
    # Direct check for orange juice in the form data
    form_text = request.form.get('text', '').lower()
    if ('orange' in form_text and 'juice' in form_text) and (not volume or volume == 0):
        print(f"Direct fix: Detected Orange Juice with missing volume, setting to 1000ml (1L)")
        volume = 1000

    print(f"log_from_label converted volume: {volume}, type: {type(volume)}")

    drink_type_id = request.form.get('drink_type_id', type=int)

    # Special case for Pepsi cans - OCR often misreads 330ml as 33ml
    if drink_type_id:
        drink_type = DrinkType.query.get(drink_type_id)
        if drink_type and drink_type.name.lower() == 'pepsi' and volume > 0 and volume < 100:
            print(f"Detected Pepsi can with small volume ({volume}ml), correcting to 330ml")
            volume = 330
        # Special case for Orange Juice - often 1L bottles
        elif drink_type and ('orange' in drink_type.name.lower() or 'juice' in drink_type.name.lower()) and (volume == 0 or not volume):
            print(f"Detected Orange Juice with missing volume, setting to 1000ml (1L)")
            volume = 1000

    if not volume or volume <= 0:
        flash('Invalid volume', 'danger')
        return redirect(url_for('dashboard'))

    # If no drink type specified, default to water (id=1)
    if not drink_type_id:
        water_type = DrinkType.query.filter_by(name='Water').first()
        drink_type_id = water_type.id if water_type else None

    # Create new water log
    new_log = WaterLog(
        amount=volume,
        user_id=current_user.id,
        drink_type_id=drink_type_id,
        input_method='ocr'
    )

    db.session.add(new_log)
    db.session.commit()

    # Get the drink type name for the flash message
    drink_name = "water"
    if drink_type_id:
        drink_type = DrinkType.query.get(drink_type_id)
        if drink_type:
            drink_name = drink_type.name.lower()

    flash(f'{volume} ml of {drink_name} logged successfully from label!', 'success')
    return redirect(url_for('dashboard'))

# Voice Recognition Route
@app.route('/voice_n8n', methods=['GET', 'POST'])
@login_required
def voice_n8n():
    """Voice input using local processing"""
    # Check if voice recognition is available
    if not voice_recognition_available:
        flash('Voice input is not available. Please install the required dependencies.', 'danger')
        return redirect(url_for('dashboard'))

    return render_template('voice_n8n.html')

# Gesture Logging Route
@app.route('/gesture_logging', methods=['GET', 'POST'])
@login_required
def gesture_logging():
    """Log water intake using hand gestures"""
    # Check if gesture recognition is available
    if not gesture_recognition_available:
        flash('Gesture logging is not available. Please install the required dependencies (OpenCV and MediaPipe).', 'danger')
        return redirect(url_for('dashboard'))

    return render_template('gesture_logging.html')

# Redirect old voice_input URL to new voice_n8n
@app.route('/voice_input', methods=['GET', 'POST'])
@login_required
def voice_input():
    """Redirect to the new voice input page"""
    return redirect(url_for('voice_n8n'))

@app.route('/api/process_voice', methods=['POST'])
@login_required
def process_voice():
    """API endpoint to process voice input locally"""
    if not request.is_json:
        return jsonify({'error': 'Request must be JSON'}), 400

    data = request.json
    text = data.get('text', '')

    if not text:
        return jsonify({'error': 'No text provided'}), 400

    try:
        # Process the text directly using local processing
        # Extract volume using regex - improved to handle uppercase ML and more formats
        import re
        volume_match = re.search(r'(\d+)\s*(ML|ml|milliliters|millilitres|oz|ounces|cups?|glasses?|bottles?|liters?|litres?)', text.lower().replace("ml", " ml"))
        volume_text = "0 ml"
        volume_ml = 0

        if volume_match:
            amount = int(volume_match.group(1))
            unit = volume_match.group(2).lower()

            # Convert to ml based on unit
            if unit in ['oz', 'ounces']:
                amount = int(amount * 29.57)  # 1 oz = 29.57 ml
            elif unit in ['cup', 'cups']:
                amount = int(amount * 237)  # 1 cup = 237 ml
            elif unit in ['glass', 'glasses']:
                amount = int(amount * 250)  # Assume 1 glass = 250 ml
            elif unit in ['bottle', 'bottles']:
                amount = int(amount * 500)  # Assume 1 bottle = 500 ml
            elif unit in ['liter', 'liters', 'litre', 'litres']:
                amount = int(amount * 1000)  # 1 liter = 1000 ml

            volume_text = f"{amount} ml"
            volume_ml = amount
        else:
            # Fallback: Try to find any number followed by ML or ml anywhere in the text
            direct_match = re.search(r'(\d+)\s*ML', text) or re.search(r'(\d+)\s*ml', text)
            if direct_match:
                amount = int(direct_match.group(1))
                volume_text = f"{amount} ml"
                volume_ml = amount
                print(f"Direct match found: {amount}ml in '{text}'")

            # Additional fallback: Look for any number in the text
            elif "200" in text or "200ml" in text or "200 ml" in text:
                volume_text = "200 ml"
                volume_ml = 200
                print(f"Hardcoded match found: 200ml in '{text}'")

        # Extract drink type
        drink_type = "Water"  # Default
        if "coffee" in text.lower():
            drink_type = "Coffee"
        elif "tea" in text.lower():
            drink_type = "Tea"
        elif "juice" in text.lower() or "orange" in text.lower():
            drink_type = "Juice"
        elif "milk" in text.lower():
            drink_type = "Milk"
        elif "soda" in text.lower() or "coke" in text.lower():
            drink_type = "Soda"
        elif "pepsi" in text.lower():
            drink_type = "Pepsi"

        # Find drink type ID
        drink_types = DrinkType.query.all()
        drink_type_id = 1  # Default to water

        for dt in drink_types:
            if dt.name.lower() == drink_type.lower():
                drink_type_id = dt.id
                break

        # Create the response
        result = {
            'volume': volume_text,
            'drink_type': drink_type,
            'drink_type_id': drink_type_id,
            'volume_ml': volume_ml,
            'text': text,
            'source': 'local_processing'
        }

        print(f"Processed voice input: '{text}' → {volume_text} of {drink_type}")

        return jsonify({
            'success': True,
            'result': result
        })

    except Exception as e:
        import traceback
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500

# API endpoint for water data
@app.route('/api/process_gesture', methods=['POST'])
@login_required
def process_gesture():
    """API endpoint to process gesture images"""
    # Check if gesture recognition is available
    if not gesture_recognition_available:
        return jsonify({'error': 'Gesture recognition is not available'}), 400

    # Check if an image was uploaded
    if 'image' not in request.files:
        return jsonify({'error': 'No image provided'}), 400

    image_file = request.files['image']
    automatic = request.form.get('automatic', 'false').lower() == 'true'

    if image_file.filename == '':
        return jsonify({'error': 'No image selected'}), 400

    try:
        # Create a temporary directory if it doesn't exist
        temp_dir = os.path.join(app.static_folder, 'uploads', 'gestures')
        os.makedirs(temp_dir, exist_ok=True)

        # Save the uploaded image
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        image_filename = f"gesture_{timestamp}.jpg"
        image_path = os.path.join(temp_dir, image_filename)
        image_file.save(image_path)

        # Process the image with the gesture detector
        result = gesture_detector.process_image(image_path)

        # Log the result for debugging
        print(f"Gesture detection result: {result}")

        # If successful, prepare the result
        if result.get('success'):
            # Get the relative path for the annotated image
            annotated_image = result.get('annotated_image')
            if annotated_image and os.path.exists(annotated_image):
                # Get the relative path for the static URL
                rel_path = os.path.relpath(annotated_image, app.static_folder)
                result['annotated_image'] = url_for('static', filename=rel_path)

            # Return the result
            return jsonify({
                'success': True,
                'result': result
            })
        else:
            # Return the error
            return jsonify({
                'success': False,
                'result': result
            })

    except Exception as e:
        import traceback
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500

@app.route('/api/water-data')
@login_required
def api_water_data():
    # Get last 7 days data
    end_date = datetime.now(timezone.utc).date()
    start_date = end_date - timedelta(days=6)

    # Query data for last 7 days
    results = db.session.query(
        db.func.date(WaterLog.timestamp).label('day'),
        db.func.sum(WaterLog.amount).label('total')
    ).filter(
        WaterLog.user_id == current_user.id,
        db.func.date(WaterLog.timestamp) >= start_date,
        db.func.date(WaterLog.timestamp) <= end_date
    ).group_by(
        db.func.date(WaterLog.timestamp)
    ).order_by(
        db.func.date(WaterLog.timestamp)
    ).all()

    # Format data for API
    data = []
    for day, total in results:
        # Check if day is already a string
        if isinstance(day, str):
            date_str = day
        else:
            date_str = day.strftime('%Y-%m-%d')
        data.append({'date': date_str, 'amount': total})

    return jsonify({
        'success': True,
        'data': data,
        'user': {
            'username': current_user.username,
            'daily_goal': current_user.daily_goal,
            'preferred_unit': current_user.preferred_unit
        }
    })

# Utility functions
def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS']

def save_uploaded_file(file, folder):
    if file and allowed_file(file.filename):
        filename = secure_filename(file.filename)
        # Add timestamp to filename to avoid duplicates
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        filename = f"{timestamp}_{filename}"

        # Check for drink type keywords in filename
        original_filename = filename.lower()

        # For Pepsi cans, use the standard pepsi_can.png image
        if 'pepsi' in original_filename and not original_filename.endswith(('.webm', '.mp3', '.wav', '.ogg', '.m4a')):
            # Check if we have a pepsi_can.png in the containers folder
            pepsi_image_path = os.path.join(app.static_folder, 'uploads', 'containers', 'pepsi_can.png')
            if os.path.exists(pepsi_image_path):
                print(f"Using standard Pepsi can image for {original_filename}")
                return 'uploads/containers/pepsi_can.png'

        # Make sure the static/uploads/containers folder exists
        containers_folder = os.path.join(app.static_folder, 'uploads', 'containers')
        os.makedirs(containers_folder, exist_ok=True)

        # Make sure the static/uploads/audio folder exists
        audio_folder = os.path.join(app.static_folder, 'uploads', 'audio')
        os.makedirs(audio_folder, exist_ok=True)

        # Ensure the target folder exists
        os.makedirs(folder, exist_ok=True)

        # Save the file
        filepath = os.path.join(folder, filename)
        file.save(filepath)
        print(f"Saved file to: {filepath}")

        # Check if this is an audio file
        if filename.lower().endswith(('.webm', '.mp3', '.wav', '.ogg', '.m4a')):
            # If this is an audio file, make sure it's in the static/uploads/audio folder
            if not filepath.startswith(audio_folder):
                # Copy the file to the audio folder
                target_path = os.path.join(audio_folder, filename)
                try:
                    shutil.copy(filepath, target_path)
                    print(f"Copied file to audio folder: {target_path}")
                    # Use the path relative to static folder
                    return os.path.join('uploads', 'audio', filename)
                except Exception as e:
                    print(f"Error copying file to audio folder: {e}")
                    # Continue with normal processing if copy fails

        # If this is a container image, make sure it's in the static/uploads/containers folder
        elif 'container' in folder.lower():
            # Check if the file is already in the correct location
            if not filepath.startswith(containers_folder):
                # Copy the file to the containers folder
                target_path = os.path.join(containers_folder, filename)
                try:
                    shutil.copy(filepath, target_path)
                    print(f"Copied file to containers folder: {target_path}")
                    # Use the path relative to static folder
                    return os.path.join('uploads', 'containers', filename)
                except Exception as e:
                    print(f"Error copying file to containers folder: {e}")

        # Determine the relative path for the database
        # If the folder is under static, extract the relative path
        static_folder = app.static_folder
        if folder.startswith(static_folder):
            relative_path = folder[len(static_folder) + 1:]  # +1 for the slash
            return os.path.join(relative_path, filename).replace('\\', '/')

        # If it's not under static, it's probably in a subfolder of uploads
        if 'uploads' in folder:
            # Check if uploads is in the path
            uploads_index = folder.find('uploads')
            if uploads_index != -1:
                relative_path = folder[uploads_index:]
                return os.path.join(relative_path, filename).replace('\\', '/')

        # Fallback to the old method
        return os.path.join('uploads', os.path.basename(folder), filename).replace('\\', '/')
    return None

# Function to create default drink types
def create_default_drink_types():
    # Define default drink types
    default_types = [
        {
            'name': 'Water',
            'hydration_factor': 1.0,
            'color': '#4DA6FF',
            'icon': 'bi-droplet-fill'
        },
        {
            'name': 'Tea',
            'hydration_factor': 0.8,
            'color': '#8B5A2B',
            'icon': 'bi-cup-hot-fill'
        },
        {
            'name': 'Coffee',
            'hydration_factor': 0.6,
            'color': '#6F4E37',
            'icon': 'bi-cup-fill'
        },
        {
            'name': 'Milk',
            'hydration_factor': 0.9,
            'color': '#F5F5F5',
            'icon': 'bi-cup-straw'
        },
        {
            'name': 'Juice',
            'hydration_factor': 0.7,
            'color': '#FFA500',
            'icon': 'bi-cup'
        },
        {
            'name': 'Soda',
            'hydration_factor': 0.3,
            'color': '#8B0000',
            'icon': 'bi-cup-straw'
        },
        {
            'name': 'Pepsi',
            'hydration_factor': 0.3,
            'color': '#0000AA',
            'icon': 'bi-cup-straw'
        }
    ]

    # Check if drink types already exist
    existing_types = DrinkType.query.all()
    if not existing_types:
        for drink_type in default_types:
            new_type = DrinkType(**drink_type)
            db.session.add(new_type)

        db.session.commit()
        print("Default drink types created successfully!")
    else:
        print("Drink types already exist.")

# Function to create demo user
def create_demo_user():
    # Check if demo user already exists
    demo_user = db.session.execute(db.select(User).filter_by(username="demo")).scalar_one_or_none()

    if not demo_user:
        # Create demo user with all required fields
        demo_user = User(
            username="demo",
            email="<EMAIL>",
            daily_goal=2000,
            preferred_unit="ml",
            theme="light",
            accent_color="blue",
            reminder_enabled=False,
            reminder_interval=60,
            gender="not_specified",
            join_date=datetime.now(timezone.utc)
        )
        demo_user.set_password("demo123")
        db.session.add(demo_user)
        db.session.commit()

        # Create some demo containers
        water_bottle = Container(
            name="Water Bottle",
            volume=500,
            user_id=demo_user.id,
            created_at=datetime.now(timezone.utc)
        )
        coffee_mug = Container(
            name="Coffee Mug",
            volume=350,
            user_id=demo_user.id,
            created_at=datetime.now(timezone.utc)
        )
        # Get the Pepsi drink type (or fallback to Soda)
        pepsi_type = DrinkType.query.filter_by(name='Pepsi').first()
        if not pepsi_type:
            pepsi_type = DrinkType.query.filter_by(name='Soda').first()

        pepsi_can = Container(
            name="Pepsi Can",
            volume=350,
            user_id=demo_user.id,
            image_path="uploads/containers/pepsi_can.png",
            drink_type_id=pepsi_type.id if pepsi_type else None,
            created_at=datetime.now(timezone.utc)
        )
        db.session.add(water_bottle)
        db.session.add(coffee_mug)
        db.session.add(pepsi_can)
        db.session.commit()

        # Create the uploads directory if it doesn't exist
        container_dir = os.path.join(app.static_folder, 'uploads', 'containers')
        os.makedirs(container_dir, exist_ok=True)

        # Copy a default pepsi can image if it doesn't exist
        pepsi_image_path = os.path.join(container_dir, 'pepsi_can.png')
        if not os.path.exists(pepsi_image_path):
            try:
                # Try to import PIL
                try:
                    from PIL import Image, ImageDraw
                    has_pil = True
                except ImportError:
                    has_pil = False
                    print("PIL (Pillow) is not installed. Skipping Pepsi can image creation in demo user.")

                if has_pil:
                    # Create a simple colored image for the pepsi can
                    img = Image.new('RGB', (200, 400), color=(0, 0, 150))  # Blue background
                    draw = ImageDraw.Draw(img)
                    draw.ellipse((50, 50, 150, 150), fill=(255, 0, 0))  # Red circle
                    # Add some text to make it look like a Pepsi can
                    draw.text((70, 200), "PEPSI", fill=(255, 255, 255))
                    img.save(pepsi_image_path)
            except Exception as e:
                print(f"Error creating pepsi can image for demo user: {e}")

        print("Demo user created successfully!")
    else:
        print("Demo user already exists.")

if __name__ == '__main__':
    # Ensure directories exist
    os.makedirs('data', exist_ok=True)
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
    os.makedirs(app.config['CONTAINER_IMAGES'], exist_ok=True)
    os.makedirs(app.config['AVATAR_IMAGES'], exist_ok=True)
    os.makedirs(app.config['AUDIO_FILES'], exist_ok=True)

    # Create placeholder images directory
    placeholder_dir = os.path.join(app.static_folder, 'images')
    os.makedirs(placeholder_dir, exist_ok=True)

    # Create container placeholder image if it doesn't exist
    container_placeholder_path = os.path.join(placeholder_dir, 'container_placeholder.png')
    if not os.path.exists(container_placeholder_path):
        try:
            # Try to import PIL
            try:
                from PIL import Image, ImageDraw, ImageFont
                has_pil = True
            except ImportError:
                has_pil = False
                print("PIL (Pillow) is not installed. Skipping image creation.")

            if has_pil:
                img = Image.new('RGB', (200, 200), color=(240, 240, 240))
                draw = ImageDraw.Draw(img)
                draw.rectangle((50, 50, 150, 150), outline=(200, 200, 200), width=2)
                draw.line((50, 50, 150, 150), fill=(200, 200, 200), width=2)
                draw.line((50, 150, 150, 50), fill=(200, 200, 200), width=2)

                # Add text
                try:
                    font = ImageFont.truetype("arial.ttf", 12)
                    draw.text((60, 160), "Container Image", fill=(100, 100, 100), font=font)
                except:
                    # If font not available, use default
                    draw.text((60, 160), "Container Image", fill=(100, 100, 100))

                img.save(container_placeholder_path)
                print(f"Created container placeholder image at {container_placeholder_path}")
        except Exception as e:
            print(f"Error creating container placeholder image: {e}")

    # Create a pepsi can image in the containers directory
    container_dir = os.path.join(app.static_folder, 'uploads', 'containers')
    os.makedirs(container_dir, exist_ok=True)

    pepsi_image_path = os.path.join(container_dir, 'pepsi_can.png')
    if not os.path.exists(pepsi_image_path):
        try:
            # Try to import PIL
            try:
                from PIL import Image, ImageDraw, ImageFont
                has_pil = True
            except ImportError:
                has_pil = False
                print("PIL (Pillow) is not installed. Skipping Pepsi can image creation.")

            if has_pil:
                # Create a more realistic Pepsi can
                img = Image.new('RGB', (200, 400), color=(0, 0, 150))  # Blue background
                draw = ImageDraw.Draw(img)

                # Can shape
                draw.rectangle((50, 50, 150, 350), fill=(0, 0, 150), outline=(200, 200, 200), width=2)

                # Pepsi logo (simplified)
                draw.ellipse((60, 80, 140, 160), fill=(255, 255, 255))  # White circle
                draw.arc((70, 90, 130, 150), start=0, end=180, fill=(255, 0, 0), width=10)  # Red arc
                draw.arc((70, 110, 130, 170), start=180, end=360, fill=(0, 0, 150), width=10)  # Blue arc

                # Add text
                try:
                    font = ImageFont.truetype("arial.ttf", 24)
                    draw.text((70, 200), "PEPSI", fill=(255, 255, 255), font=font)
                    draw.text((60, 240), "350ml", fill=(255, 255, 255), font=font)
                except:
                    # If font not available, use default
                    draw.text((70, 200), "PEPSI", fill=(255, 255, 255))
                    draw.text((60, 240), "350ml", fill=(255, 255, 255))

                # Add some details
                draw.rectangle((50, 280, 150, 300), fill=(200, 200, 200))  # Silver band

                img.save(pepsi_image_path)
                print(f"Created pepsi can image at {pepsi_image_path}")
        except Exception as e:
            print(f"Error creating pepsi can image: {e}")

    # Initialize Smart Hydration feature
    try:
        from water_tracker.smart_hydration import init_app as init_smart_hydration
        init_smart_hydration(app)
        print("Smart Hydration feature initialized")
    except Exception as e:
        import traceback
        print(f"Error initializing Smart Hydration feature: {e}")
        traceback.print_exc()

    with app.app_context():
        # Create tables if they don't exist
        db.create_all()

        # Check if we need to initialize data
        if not DrinkType.query.first():
            # Create default drink types
            create_default_drink_types()

            # Create demo user
            create_demo_user()

    app.run(debug=True, host='127.0.0.1', port=8080)

