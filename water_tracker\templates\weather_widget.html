<!-- Weather Widget Template for Smart Hydration Feature -->
<div class="card mb-4 weather-widget">
    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
        <h5 class="mb-0"><i class="bi bi-cloud"></i> Weather & Hydration</h5>
        <button type="button" class="btn btn-sm btn-light refresh-weather" title="Refresh Weather Data">
            <i class="bi bi-arrow-clockwise"></i>
        </button>
    </div>
    <div class="card-body">
        <div class="weather-loading text-center py-3">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2 mb-0">Fetching weather data...</p>
        </div>

        <div class="weather-error text-center py-3" style="display: none;">
            <i class="bi bi-exclamation-triangle text-warning fs-1"></i>
            <p class="mt-2 mb-0 weather-error-message">Unexpected token '<', '&lt;!doctype '... is not valid JSON</p>
            <button class="btn btn-sm btn-primary mt-2 retry-weather">Try Again</button>
        </div>

        <div class="weather-content" style="display: none;">
            <div class="row align-items-center mb-3">
                <div class="col-md-6 text-center text-md-start">
                    <div class="d-flex align-items-center">
                        <div class="weather-icon me-2">
                            <!-- Weather icon will be inserted here by JS -->
                        </div>
                        <div>
                            <h3 class="mb-0 weather-temp">--°C</h3>
                            <p class="mb-0 text-muted weather-condition">--</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 text-center text-md-end">
                    <div class="weather-details">
                        <p class="mb-1"><i class="bi bi-droplet-fill"></i> Humidity: <span class="weather-humidity">--%</span></p>
                        <p class="mb-0"><i class="bi bi-geo-alt"></i> <span class="weather-location">--</span></p>
                    </div>
                </div>
            </div>

            <hr>

            <div class="hydration-recommendation">
                <h5 class="mb-3">Today's Hydration Recommendation</h5>
                <div class="d-flex align-items-center mb-3">
                    <div class="progress flex-grow-1 me-2" style="height: 25px;">
                        <div class="progress-bar hydration-progress" role="progressbar" style="width: 0%;"
                             aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
                    </div>
                    <div class="hydration-total fw-bold">0 ml</div>
                </div>

                <div class="hydration-explanation mb-3">
                    <!-- Explanation text will be inserted here by JS -->
                </div>

                <div class="hydration-adjustments">
                    <div class="row">
                        <div class="col-md-4 mb-2">
                            <div class="card bg-light">
                                <div class="card-body p-2 text-center">
                                    <small class="text-muted">Base</small>
                                    <h6 class="mb-0 hydration-base">2000 ml</h6>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-2">
                            <div class="card bg-light">
                                <div class="card-body p-2 text-center">
                                    <small class="text-muted">Weather</small>
                                    <h6 class="mb-0 hydration-weather">+0 ml</h6>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 mb-2">
                            <div class="card bg-light">
                                <div class="card-body p-2 text-center">
                                    <small class="text-muted">Activity</small>
                                    <h6 class="mb-0 hydration-activity">+0 ml</h6>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="text-center mt-3">
                    <button class="btn btn-primary update-goal">
                        <i class="bi bi-check-circle"></i> Update Daily Goal
                    </button>
                </div>
            </div>
        </div>
    </div>


</div>