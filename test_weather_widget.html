<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Weather Widget Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <style>
        body {
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-controls {
            margin-bottom: 20px;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">Weather Widget Test Environment</h1>

        <div class="test-controls">
            <h4>Test Controls</h4>
            <div class="mb-3">
                <button id="test-success" class="btn btn-success">Test Success Response</button>
                <button id="test-error" class="btn btn-danger">Test Error Response</button>
                <button id="reset-widget" class="btn btn-secondary">Reset Widget</button>
            </div>
            <div class="form-check mb-3">
                <input class="form-check-input" type="checkbox" id="use-mock-data" checked>
                <label class="form-check-label" for="use-mock-data">
                    Use Mock Data (no actual API calls)
                </label>
            </div>
            <div id="api-status" class="alert alert-info">
                Status: Ready for testing
            </div>
        </div>

        <!-- Weather Widget -->
        <div class="card mb-4 weather-widget">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="bi bi-cloud"></i> Weather & Hydration</h5>
                <div>
                    <input type="text" class="form-control form-control-sm d-inline-block" id="city-input" value="New York" style="width: 100px;">
                    <button type="button" class="btn btn-sm btn-light location-btn" title="Get Location">
                        <i class="bi bi-geo-alt"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-light refresh-weather" title="Refresh Weather Data">
                        <i class="bi bi-arrow-clockwise"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="weather-loading text-center py-3" style="display: none;">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 mb-0">Fetching weather data...</p>
                </div>

                <div class="weather-error text-center py-3" style="display: none;">
                    <i class="bi bi-exclamation-triangle text-warning fs-1"></i>
                    <p class="mt-2 mb-0 weather-error-message">Could not fetch weather data.</p>
                    <button class="btn btn-sm btn-primary mt-2 retry-weather">Try Again</button>
                </div>

                <div class="weather-content">
                    <div class="row align-items-center mb-3">
                        <div class="col-md-6 text-center text-md-start">
                            <div class="d-flex align-items-center">
                                <div class="weather-icon me-2">
                                    <img src="https://openweathermap.org/img/wn/<EMAIL>" alt="clear sky" width="50" height="50">
                                </div>
                                <div>
                                    <h3 class="mb-0 weather-temp">25°C</h3>
                                    <p class="mb-0 text-muted weather-condition">clear sky</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 text-center text-md-end">
                            <div class="weather-details">
                                <p class="mb-1"><i class="bi bi-droplet-fill"></i> Humidity: <span class="weather-humidity">60%</span></p>
                                <p class="mb-0"><i class="bi bi-geo-alt"></i> <span class="weather-location">New York, US</span></p>
                            </div>
                        </div>
                    </div>

                    <hr>

                    <div class="hydration-recommendation">
                        <h5 class="mb-3">Today's Hydration Recommendation</h5>
                        <div class="d-flex align-items-center mb-3">
                            <div class="progress flex-grow-1 me-2" style="height: 25px;">
                                <div class="progress-bar hydration-progress bg-primary" role="progressbar" style="width: 29%;"
                                     aria-valuenow="29" aria-valuemin="0" aria-valuemax="100">29%</div>
                            </div>
                            <div class="hydration-total fw-bold">2300 ml</div>
                        </div>

                        <div class="hydration-explanation mb-3">
                            Based on the current weather and your activity level, we recommend drinking 2300 ml of water today.
                        </div>

                        <div class="hydration-adjustments">
                            <div class="row">
                                <div class="col-md-4 mb-2">
                                    <div class="card bg-light">
                                        <div class="card-body p-2 text-center">
                                            <small class="text-muted">Base</small>
                                            <h6 class="mb-0 hydration-base">2000 ml</h6>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-2">
                                    <div class="card bg-light">
                                        <div class="card-body p-2 text-center">
                                            <small class="text-muted">Weather</small>
                                            <h6 class="mb-0 hydration-weather">+200 ml</h6>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4 mb-2">
                                    <div class="card bg-light">
                                        <div class="card-body p-2 text-center">
                                            <small class="text-muted">Activity</small>
                                            <h6 class="mb-0 hydration-activity">+100 ml</h6>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="text-center mt-3">
                            <button class="btn btn-primary update-goal">
                                <i class="bi bi-check-circle"></i> Update Daily Goal
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="test_weather_script.js"></script>
</body>
</html>
