"""
Gesture recognition module for Water Intake Tracker
This module detects hand gestures using OpenCV and MediaPipe
"""

import os
import logging
import cv2
import numpy as np
import time
import json
from datetime import datetime

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Try to import MediaPipe
try:
    import mediapipe as mp
    mediapipe_available = True
    logger.info("MediaPipe is available")
except ImportError:
    mediapipe_available = False
    logger.warning("MediaPipe is not available. Gesture recognition will be limited.")

class GestureDetector:
    """Class for detecting hand gestures"""
    
    def __init__(self):
        """Initialize the gesture detector"""
        self.mediapipe_available = mediapipe_available
        
        if self.mediapipe_available:
            self.mp_hands = mp.solutions.hands
            self.mp_drawing = mp.solutions.drawing_utils
            self.mp_drawing_styles = mp.solutions.drawing_styles
            self.hands = self.mp_hands.Hands(
                static_image_mode=False,
                max_num_hands=1,
                min_detection_confidence=0.5,
                min_tracking_confidence=0.5
            )
            logger.info("MediaPipe Hands model initialized")
        else:
            logger.warning("MediaPipe not available, using fallback detection")
    
    def process_image(self, image_path):
        """Process an image and detect gestures"""
        try:
            # Check if the image exists
            if not os.path.exists(image_path):
                logger.error(f"Image file not found: {image_path}")
                return {
                    'success': False,
                    'error': f"Image file not found: {image_path}",
                    'gesture': None
                }
            
            # Read the image
            image = cv2.imread(image_path)
            if image is None:
                logger.error(f"Failed to read image: {image_path}")
                return {
                    'success': False,
                    'error': f"Failed to read image: {image_path}",
                    'gesture': None
                }
            
            # Process the image
            return self._process_frame(image, save_path=image_path + "_processed.jpg")
            
        except Exception as e:
            logger.error(f"Error processing image: {e}")
            import traceback
            traceback.print_exc()
            return {
                'success': False,
                'error': str(e),
                'gesture': None
            }
    
    def _process_frame(self, frame, save_path=None):
        """Process a single frame to detect gestures"""
        if not self.mediapipe_available:
            logger.warning("MediaPipe not available, using fallback detection")
            return self._fallback_detection(frame, save_path)
        
        try:
            # Convert BGR to RGB
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            
            # Process the frame with MediaPipe
            results = self.hands.process(rgb_frame)
            
            # Draw hand landmarks on the image
            annotated_frame = frame.copy()
            
            # Check if hand landmarks are detected
            if results.multi_hand_landmarks:
                for hand_landmarks in results.multi_hand_landmarks:
                    # Draw the landmarks
                    self.mp_drawing.draw_landmarks(
                        annotated_frame,
                        hand_landmarks,
                        self.mp_hands.HAND_CONNECTIONS,
                        self.mp_drawing_styles.get_default_hand_landmarks_style(),
                        self.mp_drawing_styles.get_default_hand_connections_style()
                    )
                    
                    # Detect specific gestures
                    gesture, confidence = self._detect_gesture(hand_landmarks)
                    
                    # Add gesture text to the image
                    cv2.putText(
                        annotated_frame,
                        f"Gesture: {gesture} ({confidence:.2f})",
                        (10, 30),
                        cv2.FONT_HERSHEY_SIMPLEX,
                        1,
                        (0, 255, 0),
                        2
                    )
                    
                    # Save the annotated image if requested
                    if save_path:
                        cv2.imwrite(save_path, annotated_frame)
                    
                    # Return the detected gesture
                    return {
                        'success': True,
                        'gesture': gesture,
                        'confidence': confidence,
                        'annotated_image': save_path if save_path else None
                    }
            
            # No hand landmarks detected
            if save_path:
                cv2.putText(
                    annotated_frame,
                    "No hand detected",
                    (10, 30),
                    cv2.FONT_HERSHEY_SIMPLEX,
                    1,
                    (0, 0, 255),
                    2
                )
                cv2.imwrite(save_path, annotated_frame)
            
            return {
                'success': False,
                'error': "No hand detected",
                'gesture': None,
                'annotated_image': save_path if save_path else None
            }
            
        except Exception as e:
            logger.error(f"Error processing frame: {e}")
            import traceback
            traceback.print_exc()
            return {
                'success': False,
                'error': str(e),
                'gesture': None
            }
    
    def _detect_gesture(self, hand_landmarks):
        """Detect specific gestures from hand landmarks"""
        # Extract landmark positions
        landmarks = []
        for landmark in hand_landmarks.landmark:
            landmarks.append((landmark.x, landmark.y, landmark.z))
        
        # Check for peace sign (V gesture)
        # Peace sign: index and middle fingers extended, others closed
        if self._is_peace_sign(landmarks):
            return "peace_sign", 0.9
        
        # Check for thumbs up
        if self._is_thumbs_up(landmarks):
            return "thumbs_up", 0.85
        
        # Default to unknown gesture
        return "unknown", 0.5
    
    def _is_peace_sign(self, landmarks):
        """Check if the hand is making a peace sign (V gesture)"""
        # Get fingertip positions
        thumb_tip = landmarks[4]
        index_tip = landmarks[8]
        middle_tip = landmarks[12]
        ring_tip = landmarks[16]
        pinky_tip = landmarks[20]
        
        # Get finger base positions
        index_base = landmarks[5]
        middle_base = landmarks[9]
        ring_base = landmarks[13]
        pinky_base = landmarks[17]
        
        # Check if index and middle fingers are extended
        index_extended = index_tip[1] < index_base[1]
        middle_extended = middle_tip[1] < middle_base[1]
        
        # Check if ring and pinky fingers are closed
        ring_closed = ring_tip[1] > ring_base[1]
        pinky_closed = pinky_tip[1] > pinky_base[1]
        
        # Peace sign: index and middle extended, ring and pinky closed
        return index_extended and middle_extended and ring_closed and pinky_closed
    
    def _is_thumbs_up(self, landmarks):
        """Check if the hand is making a thumbs up gesture"""
        # Get fingertip positions
        thumb_tip = landmarks[4]
        index_tip = landmarks[8]
        middle_tip = landmarks[12]
        ring_tip = landmarks[16]
        pinky_tip = landmarks[20]
        
        # Get finger base positions
        thumb_base = landmarks[2]
        index_base = landmarks[5]
        middle_base = landmarks[9]
        ring_base = landmarks[13]
        pinky_base = landmarks[17]
        
        # Check if thumb is extended upward
        thumb_extended = thumb_tip[1] < thumb_base[1]
        
        # Check if other fingers are closed
        other_fingers_closed = (
            index_tip[1] > index_base[1] and
            middle_tip[1] > middle_base[1] and
            ring_tip[1] > ring_base[1] and
            pinky_tip[1] > pinky_base[1]
        )
        
        # Thumbs up: thumb extended upward, other fingers closed
        return thumb_extended and other_fingers_closed
    
    def _fallback_detection(self, frame, save_path=None):
        """Fallback method when MediaPipe is not available"""
        # Add text to the image
        annotated_frame = frame.copy()
        cv2.putText(
            annotated_frame,
            "MediaPipe not available",
            (10, 30),
            cv2.FONT_HERSHEY_SIMPLEX,
            1,
            (0, 0, 255),
            2
        )
        
        # Save the annotated image if requested
        if save_path:
            cv2.imwrite(save_path, annotated_frame)
        
        return {
            'success': False,
            'error': "MediaPipe not available",
            'gesture': None,
            'annotated_image': save_path if save_path else None
        }

# Create a global instance
gesture_detector = GestureDetector()
