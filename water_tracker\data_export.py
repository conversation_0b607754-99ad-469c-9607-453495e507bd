"""
Data Export Module for Water Intake Tracker
Provides functionality to export water intake data in various formats
"""

import json
import io
import os
from datetime import datetime, timedelta
from flask import current_app
from io import BytesIO
import base64

# Try to import optional dependencies
try:
    import pandas as pd
    pandas_available = True
except ImportError:
    pandas_available = False

try:
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.lib import colors
    reportlab_available = True
except ImportError:
    reportlab_available = False

try:
    import matplotlib.pyplot as plt
    import matplotlib.dates as mdates
    matplotlib_available = True
except ImportError:
    matplotlib_available = False


class DataExporter:
    """Main class for exporting water intake data"""

    def __init__(self, user_id):
        self.user_id = user_id

    def get_water_logs(self, start_date=None, end_date=None, drink_types=None):
        """Get water logs for the specified date range and drink types"""
        from water_tracker.app import WaterLog, DrinkType, db

        query = WaterLog.query.filter_by(user_id=self.user_id)

        if start_date:
            query = query.filter(WaterLog.timestamp >= start_date)
        if end_date:
            query = query.filter(WaterLog.timestamp <= end_date)
        if drink_types:
            query = query.filter(WaterLog.drink_type_id.in_(drink_types))

        logs = query.order_by(WaterLog.timestamp.desc()).all()

        # Convert to list of dictionaries
        data = []
        for log in logs:
            drink_type_name = "Unknown"
            if log.drink_type:
                drink_type_name = log.drink_type.name

            data.append({
                'id': log.id,
                'amount': log.amount,
                'timestamp': log.timestamp,
                'drink_type': drink_type_name,
                'input_method': log.input_method,
                'notes': log.notes or ''
            })

        return data

    def export_csv(self, start_date=None, end_date=None, drink_types=None):
        """Export data as CSV"""
        data = self.get_water_logs(start_date, end_date, drink_types)

        if not data:
            return None

        if pandas_available:
            # Use pandas if available
            df = pd.DataFrame(data)

            # Format timestamp for better readability
            df['timestamp'] = pd.to_datetime(df['timestamp']).dt.strftime('%Y-%m-%d %H:%M:%S')

            # Reorder columns
            df = df[['timestamp', 'amount', 'drink_type', 'input_method', 'notes']]

            # Create CSV string
            csv_buffer = io.StringIO()
            df.to_csv(csv_buffer, index=False)

            return csv_buffer.getvalue()
        else:
            # Manual CSV creation without pandas
            csv_buffer = io.StringIO()

            # Write header
            csv_buffer.write('timestamp,amount,drink_type,input_method,notes\n')

            # Write data rows
            for item in data:
                timestamp = item['timestamp']
                if isinstance(timestamp, datetime):
                    timestamp_str = timestamp.strftime('%Y-%m-%d %H:%M:%S')
                else:
                    timestamp_str = str(timestamp)

                # Escape commas and quotes in notes
                notes = str(item['notes'] or '').replace('"', '""')
                if ',' in notes or '"' in notes:
                    notes = f'"{notes}"'

                csv_buffer.write(f'{timestamp_str},{item["amount"]},{item["drink_type"]},{item["input_method"]},{notes}\n')

            return csv_buffer.getvalue()

    def export_json(self, start_date=None, end_date=None, drink_types=None):
        """Export data as JSON"""
        data = self.get_water_logs(start_date, end_date, drink_types)

        if not data:
            return None

        # Convert datetime objects to strings for JSON serialization
        for item in data:
            if isinstance(item['timestamp'], datetime):
                item['timestamp'] = item['timestamp'].isoformat()

        export_data = {
            'export_date': datetime.now().isoformat(),
            'user_id': self.user_id,
            'date_range': {
                'start': start_date.isoformat() if start_date else None,
                'end': end_date.isoformat() if end_date else None
            },
            'total_records': len(data),
            'data': data
        }

        return json.dumps(export_data, indent=2)

    def create_chart(self, data, chart_type='daily'):
        """Create a chart from the data"""
        if not data or not matplotlib_available:
            return None

        if pandas_available:
            # Use pandas for data processing
            df = pd.DataFrame(data)
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df['date'] = df['timestamp'].dt.date

            # Group by date and sum amounts
            daily_data = df.groupby('date')['amount'].sum().reset_index()
            dates = daily_data['date']
            amounts = daily_data['amount']
        else:
            # Manual data processing without pandas
            daily_totals = {}
            for item in data:
                timestamp = item['timestamp']
                if isinstance(timestamp, str):
                    timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                date = timestamp.date()

                if date not in daily_totals:
                    daily_totals[date] = 0
                daily_totals[date] += item['amount']

            dates = sorted(daily_totals.keys())
            amounts = [daily_totals[date] for date in dates]

        # Create matplotlib chart
        plt.figure(figsize=(10, 6))
        plt.plot(dates, amounts, marker='o', linewidth=2, markersize=6)
        plt.title('Daily Water Intake', fontsize=16, fontweight='bold')
        plt.xlabel('Date', fontsize=12)
        plt.ylabel('Amount (ml)', fontsize=12)
        plt.grid(True, alpha=0.3)
        plt.xticks(rotation=45)
        plt.tight_layout()

        # Save to bytes
        img_buffer = BytesIO()
        plt.savefig(img_buffer, format='png', dpi=300, bbox_inches='tight')
        img_buffer.seek(0)
        plt.close()

        return img_buffer

    def export_pdf(self, start_date=None, end_date=None, drink_types=None, include_charts=True):
        """Export data as PDF report"""
        if not reportlab_available:
            # Return a simple text-based report if ReportLab is not available
            return self._create_text_report(start_date, end_date, drink_types)

        data = self.get_water_logs(start_date, end_date, drink_types)

        if not data:
            return None

        # Create PDF buffer
        pdf_buffer = BytesIO()
        doc = SimpleDocTemplate(pdf_buffer, pagesize=A4)

        # Get styles
        styles = getSampleStyleSheet()
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            alignment=1  # Center alignment
        )

        # Build PDF content
        story = []

        # Title
        title = Paragraph("Water Intake Report", title_style)
        story.append(title)
        story.append(Spacer(1, 20))

        # Report info
        report_info = f"""
        <b>Report Generated:</b> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}<br/>
        <b>Date Range:</b> {start_date.strftime('%Y-%m-%d') if start_date else 'All time'} to {end_date.strftime('%Y-%m-%d') if end_date else 'Present'}<br/>
        <b>Total Records:</b> {len(data)}<br/>
        <b>Total Volume:</b> {sum(item['amount'] for item in data)} ml
        """
        story.append(Paragraph(report_info, styles['Normal']))
        story.append(Spacer(1, 20))

        # Add chart if requested
        if include_charts and data:
            chart_buffer = self.create_chart(data)
            if chart_buffer:
                # Save chart as temporary file
                chart_path = os.path.join(current_app.static_folder, 'temp_chart.png')
                with open(chart_path, 'wb') as f:
                    f.write(chart_buffer.getvalue())

                # Add chart to PDF
                chart_img = Image(chart_path, width=6*inch, height=3.6*inch)
                story.append(chart_img)
                story.append(Spacer(1, 20))

                # Clean up temporary file
                try:
                    os.remove(chart_path)
                except:
                    pass

        # Create data table
        if data:
            # Prepare table data
            table_data = [['Date', 'Time', 'Amount (ml)', 'Drink Type', 'Method']]

            for item in data:
                timestamp = item['timestamp']
                if isinstance(timestamp, str):
                    timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))

                table_data.append([
                    timestamp.strftime('%Y-%m-%d'),
                    timestamp.strftime('%H:%M:%S'),
                    str(item['amount']),
                    item['drink_type'],
                    item['input_method']
                ])

            # Create table
            table = Table(table_data)
            table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))

            story.append(table)

        # Build PDF
        doc.build(story)
        pdf_buffer.seek(0)

        return pdf_buffer.getvalue()

    def _create_text_report(self, start_date=None, end_date=None, drink_types=None):
        """Create a simple text-based report when PDF libraries are not available"""
        data = self.get_water_logs(start_date, end_date, drink_types)

        if not data:
            return None

        # Create text report
        report_lines = []
        report_lines.append("WATER INTAKE REPORT")
        report_lines.append("=" * 50)
        report_lines.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        if start_date:
            report_lines.append(f"Start Date: {start_date.strftime('%Y-%m-%d')}")
        if end_date:
            report_lines.append(f"End Date: {end_date.strftime('%Y-%m-%d')}")

        report_lines.append(f"Total Records: {len(data)}")
        report_lines.append(f"Total Volume: {sum(item['amount'] for item in data)} ml")
        report_lines.append("")

        # Add data table
        report_lines.append("DETAILED DATA:")
        report_lines.append("-" * 50)
        report_lines.append("Date\t\tTime\t\tAmount\tDrink Type\tMethod")
        report_lines.append("-" * 50)

        for item in data:
            timestamp = item['timestamp']
            if isinstance(timestamp, str):
                timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))

            date_str = timestamp.strftime('%Y-%m-%d')
            time_str = timestamp.strftime('%H:%M:%S')

            report_lines.append(f"{date_str}\t{time_str}\t{item['amount']} ml\t{item['drink_type']}\t{item['input_method']}")

        report_text = '\n'.join(report_lines)
        return report_text.encode('utf-8')

    def get_summary_stats(self, start_date=None, end_date=None):
        """Get summary statistics for the data"""
        data = self.get_water_logs(start_date, end_date)

        if not data:
            return {}

        if pandas_available:
            # Use pandas for calculations
            df = pd.DataFrame(data)
            df['timestamp'] = pd.to_datetime(df['timestamp'])
            df['date'] = df['timestamp'].dt.date

            # Calculate statistics
            total_volume = df['amount'].sum()
            daily_avg = df.groupby('date')['amount'].sum().mean()
            max_daily = df.groupby('date')['amount'].sum().max()
            min_daily = df.groupby('date')['amount'].sum().min()

            # Drink type breakdown
            drink_breakdown = df.groupby('drink_type')['amount'].sum().to_dict()

            # Input method breakdown
            method_breakdown = df.groupby('input_method')['amount'].sum().to_dict()

            total_days = len(df['date'].unique())
        else:
            # Manual calculations without pandas
            total_volume = sum(item['amount'] for item in data)

            # Group by date
            daily_totals = {}
            drink_breakdown = {}
            method_breakdown = {}

            for item in data:
                timestamp = item['timestamp']
                if isinstance(timestamp, str):
                    timestamp = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                date = timestamp.date()

                # Daily totals
                if date not in daily_totals:
                    daily_totals[date] = 0
                daily_totals[date] += item['amount']

                # Drink type breakdown
                drink_type = item['drink_type']
                if drink_type not in drink_breakdown:
                    drink_breakdown[drink_type] = 0
                drink_breakdown[drink_type] += item['amount']

                # Method breakdown
                method = item['input_method']
                if method not in method_breakdown:
                    method_breakdown[method] = 0
                method_breakdown[method] += item['amount']

            daily_amounts = list(daily_totals.values())
            daily_avg = sum(daily_amounts) / len(daily_amounts) if daily_amounts else 0
            max_daily = max(daily_amounts) if daily_amounts else 0
            min_daily = min(daily_amounts) if daily_amounts else 0
            total_days = len(daily_totals)

        return {
            'total_volume': round(total_volume, 2),
            'daily_average': round(daily_avg, 2),
            'max_daily': round(max_daily, 2),
            'min_daily': round(min_daily, 2),
            'total_days': total_days,
            'total_logs': len(data),
            'drink_breakdown': drink_breakdown,
            'method_breakdown': method_breakdown
        }
