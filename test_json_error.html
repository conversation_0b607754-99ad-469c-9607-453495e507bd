<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Weather Widget Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
    <style>
        body {
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">Weather Widget Test</h1>
        
        <div class="card mb-4 weather-widget">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="bi bi-cloud"></i> Weather & Hydration</h5>
                <div>
                    <input type="text" class="form-control form-control-sm d-inline-block" id="city-input" value="New York" style="width: 100px;">
                    <button type="button" class="btn btn-sm btn-light location-btn" title="Get Location">
                        <i class="bi bi-geo-alt"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-light refresh-weather" title="Refresh Weather Data">
                        <i class="bi bi-arrow-clockwise"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="weather-error text-center py-3">
                    <i class="bi bi-exclamation-triangle text-warning fs-1"></i>
                    <p class="mt-2 mb-0 weather-error-message">Unexpected token '<', '&lt;!doctype '... is not valid JSON</p>
                    <button class="btn btn-sm btn-primary mt-2 retry-weather">Try Again</button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const retryBtn = document.querySelector('.retry-weather');
            if (retryBtn) {
                retryBtn.addEventListener('click', function() {
                    alert('This is a static test page showing the exact error message from the screenshot.');
                });
            }
        });
    </script>
</body>
</html>
