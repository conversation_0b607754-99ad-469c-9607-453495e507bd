@echo off
echo Setting up gesture recognition environment...

REM Check if Python 3.9 is installed
python --version 2>NUL
if %ERRORLEVEL% NEQ 0 (
    echo Python is not installed or not in PATH.
    echo Please install Python 3.9 from https://www.python.org/downloads/release/python-3913/
    exit /b 1
)

REM Create virtual environment if it doesn't exist
if not exist "gesture-env" (
    echo Creating virtual environment...
    python -m venv gesture-env
) else (
    echo Virtual environment already exists.
)

REM Activate virtual environment and install dependencies
echo Activating virtual environment and installing dependencies...
call gesture-env\Scripts\activate.bat

REM Install required packages
echo Installing required packages...
pip install opencv-python mediapipe requests

REM Create the gesture recognition script
echo Creating gesture recognition script...

REM Create the directory for the script
if not exist "gesture_recognition" mkdir gesture_recognition

REM Create the script
echo import cv2 > gesture_recognition\gesture_recognizer.py
echo import mediapipe as mp >> gesture_recognition\gesture_recognizer.py
echo import numpy as np >> gesture_recognition\gesture_recognizer.py
echo import json >> gesture_recognition\gesture_recognizer.py
echo import os >> gesture_recognition\gesture_recognizer.py
echo import sys >> gesture_recognition\gesture_recognizer.py
echo import time >> gesture_recognition\gesture_recognizer.py
echo import requests >> gesture_recognition\gesture_recognizer.py
echo from datetime import datetime >> gesture_recognition\gesture_recognizer.py
echo. >> gesture_recognition\gesture_recognizer.py
echo # MediaPipe hands model >> gesture_recognition\gesture_recognizer.py
echo mp_hands = mp.solutions.hands >> gesture_recognition\gesture_recognizer.py
echo mp_drawing = mp.solutions.drawing_utils >> gesture_recognition\gesture_recognizer.py
echo mp_drawing_styles = mp.solutions.drawing_styles >> gesture_recognition\gesture_recognizer.py
echo. >> gesture_recognition\gesture_recognizer.py
echo def detect_gesture(frame): >> gesture_recognition\gesture_recognizer.py
echo     """Detect hand gestures in a frame""" >> gesture_recognition\gesture_recognizer.py
echo     with mp_hands.Hands( >> gesture_recognition\gesture_recognizer.py
echo         static_image_mode=False, >> gesture_recognition\gesture_recognizer.py
echo         max_num_hands=1, >> gesture_recognition\gesture_recognizer.py
echo         min_detection_confidence=0.5) as hands: >> gesture_recognition\gesture_recognizer.py
echo. >> gesture_recognition\gesture_recognizer.py
echo         # Convert the BGR image to RGB >> gesture_recognition\gesture_recognizer.py
echo         rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB) >> gesture_recognition\gesture_recognizer.py
echo. >> gesture_recognition\gesture_recognizer.py
echo         # Process the image and detect hands >> gesture_recognition\gesture_recognizer.py
echo         results = hands.process(rgb_frame) >> gesture_recognition\gesture_recognizer.py
echo. >> gesture_recognition\gesture_recognizer.py
echo         # Draw hand landmarks on the image >> gesture_recognition\gesture_recognizer.py
echo         annotated_frame = frame.copy() >> gesture_recognition\gesture_recognizer.py
echo. >> gesture_recognition\gesture_recognizer.py
echo         gesture_detected = None >> gesture_recognition\gesture_recognizer.py
echo         confidence = 0.0 >> gesture_recognition\gesture_recognizer.py
echo. >> gesture_recognition\gesture_recognizer.py
echo         if results.multi_hand_landmarks: >> gesture_recognition\gesture_recognizer.py
echo             for hand_landmarks in results.multi_hand_landmarks: >> gesture_recognition\gesture_recognizer.py
echo                 # Draw the hand landmarks >> gesture_recognition\gesture_recognizer.py
echo                 mp_drawing.draw_landmarks( >> gesture_recognition\gesture_recognizer.py
echo                     annotated_frame, >> gesture_recognition\gesture_recognizer.py
echo                     hand_landmarks, >> gesture_recognition\gesture_recognizer.py
echo                     mp_hands.HAND_CONNECTIONS, >> gesture_recognition\gesture_recognizer.py
echo                     mp_drawing_styles.get_default_hand_landmarks_style(), >> gesture_recognition\gesture_recognizer.py
echo                     mp_drawing_styles.get_default_hand_connections_style()) >> gesture_recognition\gesture_recognizer.py
echo. >> gesture_recognition\gesture_recognizer.py
echo                 # Get landmark positions >> gesture_recognition\gesture_recognizer.py
echo                 landmarks = [] >> gesture_recognition\gesture_recognizer.py
echo                 for landmark in hand_landmarks.landmark: >> gesture_recognition\gesture_recognizer.py
echo                     landmarks.append([landmark.x, landmark.y, landmark.z]) >> gesture_recognition\gesture_recognizer.py
echo. >> gesture_recognition\gesture_recognizer.py
echo                 # Detect peace sign (V sign) >> gesture_recognition\gesture_recognizer.py
echo                 if is_peace_sign(landmarks): >> gesture_recognition\gesture_recognizer.py
echo                     gesture_detected = "peace_sign" >> gesture_recognition\gesture_recognizer.py
echo                     confidence = 0.9 >> gesture_recognition\gesture_recognizer.py
echo                     cv2.putText(annotated_frame, "Peace Sign", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2) >> gesture_recognition\gesture_recognizer.py
echo. >> gesture_recognition\gesture_recognizer.py
echo                 # Detect thumbs up >> gesture_recognition\gesture_recognizer.py
echo                 elif is_thumbs_up(landmarks): >> gesture_recognition\gesture_recognizer.py
echo                     gesture_detected = "thumbs_up" >> gesture_recognition\gesture_recognizer.py
echo                     confidence = 0.9 >> gesture_recognition\gesture_recognizer.py
echo                     cv2.putText(annotated_frame, "Thumbs Up", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2) >> gesture_recognition\gesture_recognizer.py
echo. >> gesture_recognition\gesture_recognizer.py
echo         return gesture_detected, confidence, annotated_frame >> gesture_recognition\gesture_recognizer.py
echo. >> gesture_recognition\gesture_recognizer.py
echo def is_peace_sign(landmarks): >> gesture_recognition\gesture_recognizer.py
echo     """Check if the hand is making a peace sign (V sign)""" >> gesture_recognition\gesture_recognizer.py
echo     # Index finger should be extended >> gesture_recognition\gesture_recognizer.py
echo     index_extended = landmarks[8][1] < landmarks[6][1] >> gesture_recognition\gesture_recognizer.py
echo. >> gesture_recognition\gesture_recognizer.py
echo     # Middle finger should be extended >> gesture_recognition\gesture_recognizer.py
echo     middle_extended = landmarks[12][1] < landmarks[10][1] >> gesture_recognition\gesture_recognizer.py
echo. >> gesture_recognition\gesture_recognizer.py
echo     # Ring and pinky fingers should be curled >> gesture_recognition\gesture_recognizer.py
echo     ring_curled = landmarks[16][1] > landmarks[14][1] >> gesture_recognition\gesture_recognizer.py
echo     pinky_curled = landmarks[20][1] > landmarks[18][1] >> gesture_recognition\gesture_recognizer.py
echo. >> gesture_recognition\gesture_recognizer.py
echo     # Thumb should not be extended in the same direction >> gesture_recognition\gesture_recognizer.py
echo     thumb_not_extended = abs(landmarks[4][0] - landmarks[8][0]) > 0.1 >> gesture_recognition\gesture_recognizer.py
echo. >> gesture_recognition\gesture_recognizer.py
echo     return index_extended and middle_extended and ring_curled and pinky_curled and thumb_not_extended >> gesture_recognition\gesture_recognizer.py
echo. >> gesture_recognition\gesture_recognizer.py
echo def is_thumbs_up(landmarks): >> gesture_recognition\gesture_recognizer.py
echo     """Check if the hand is making a thumbs up gesture""" >> gesture_recognition\gesture_recognizer.py
echo     # Thumb should be extended upward >> gesture_recognition\gesture_recognizer.py
echo     thumb_extended = landmarks[4][1] < landmarks[3][1] and landmarks[3][1] < landmarks[2][1] >> gesture_recognition\gesture_recognizer.py
echo. >> gesture_recognition\gesture_recognizer.py
echo     # All other fingers should be curled >> gesture_recognition\gesture_recognizer.py
echo     index_curled = landmarks[8][1] > landmarks[6][1] >> gesture_recognition\gesture_recognizer.py
echo     middle_curled = landmarks[12][1] > landmarks[10][1] >> gesture_recognition\gesture_recognizer.py
echo     ring_curled = landmarks[16][1] > landmarks[14][1] >> gesture_recognition\gesture_recognizer.py
echo     pinky_curled = landmarks[20][1] > landmarks[18][1] >> gesture_recognition\gesture_recognizer.py
echo. >> gesture_recognition\gesture_recognizer.py
echo     return thumb_extended and index_curled and middle_curled and ring_curled and pinky_curled >> gesture_recognition\gesture_recognizer.py
echo. >> gesture_recognition\gesture_recognizer.py
echo def log_water_directly(amount, gesture_type): >> gesture_recognition\gesture_recognizer.py
echo     """Log water directly to the app using the /log_water endpoint""" >> gesture_recognition\gesture_recognizer.py
echo     try: >> gesture_recognition\gesture_recognizer.py
echo         # Create the payload >> gesture_recognition\gesture_recognizer.py
echo         data = { >> gesture_recognition\gesture_recognizer.py
echo             'amount': amount, >> gesture_recognition\gesture_recognizer.py
echo             'drink_type_id': 1,  # Water >> gesture_recognition\gesture_recognizer.py
echo             'notes': f'Logged via gesture: {gesture_type}' >> gesture_recognition\gesture_recognizer.py
echo         } >> gesture_recognition\gesture_recognizer.py
echo. >> gesture_recognition\gesture_recognizer.py
echo         # Send the request to the app >> gesture_recognition\gesture_recognizer.py
echo         response = requests.post('http://127.0.0.1:8080/log_water', data=data) >> gesture_recognition\gesture_recognizer.py
echo. >> gesture_recognition\gesture_recognizer.py
echo         # Check if the request was successful >> gesture_recognition\gesture_recognizer.py
echo         if response.status_code == 200: >> gesture_recognition\gesture_recognizer.py
echo             return True, "Water logged successfully!" >> gesture_recognition\gesture_recognizer.py
echo         else: >> gesture_recognition\gesture_recognizer.py
echo             return False, f"Error logging water: {response.status_code}" >> gesture_recognition\gesture_recognizer.py
echo     except Exception as e: >> gesture_recognition\gesture_recognizer.py
echo         return False, f"Error logging water: {str(e)}" >> gesture_recognition\gesture_recognizer.py
echo. >> gesture_recognition\gesture_recognizer.py
echo def main(): >> gesture_recognition\gesture_recognizer.py
echo     """Main function""" >> gesture_recognition\gesture_recognizer.py
echo     # Initialize webcam >> gesture_recognition\gesture_recognizer.py
echo     cap = cv2.VideoCapture(0) >> gesture_recognition\gesture_recognizer.py
echo. >> gesture_recognition\gesture_recognizer.py
echo     if not cap.isOpened(): >> gesture_recognition\gesture_recognizer.py
echo         print("Error: Could not open webcam.") >> gesture_recognition\gesture_recognizer.py
echo         result = { >> gesture_recognition\gesture_recognizer.py
echo             "success": False, >> gesture_recognition\gesture_recognizer.py
echo             "error": "Could not open webcam", >> gesture_recognition\gesture_recognizer.py
echo             "gesture": None, >> gesture_recognition\gesture_recognizer.py
echo             "annotated_image": None >> gesture_recognition\gesture_recognizer.py
echo         } >> gesture_recognition\gesture_recognizer.py
echo         with open("gesture_result.json", "w") as f: >> gesture_recognition\gesture_recognizer.py
echo             json.dump(result, f) >> gesture_recognition\gesture_recognizer.py
echo         return >> gesture_recognition\gesture_recognizer.py
echo. >> gesture_recognition\gesture_recognizer.py
echo     # Create window >> gesture_recognition\gesture_recognizer.py
echo     cv2.namedWindow("Gesture Recognition", cv2.WINDOW_NORMAL) >> gesture_recognition\gesture_recognizer.py
echo     cv2.resizeWindow("Gesture Recognition", 640, 480) >> gesture_recognition\gesture_recognizer.py
echo. >> gesture_recognition\gesture_recognizer.py
echo     # Variables for gesture detection >> gesture_recognition\gesture_recognizer.py
echo     gesture_frames = 0 >> gesture_recognition\gesture_recognizer.py
echo     current_gesture = None >> gesture_recognition\gesture_recognizer.py
echo     gesture_start_time = None >> gesture_recognition\gesture_recognizer.py
echo     gesture_detected = False >> gesture_recognition\gesture_recognizer.py
echo. >> gesture_recognition\gesture_recognizer.py
echo     try: >> gesture_recognition\gesture_recognizer.py
echo         while True: >> gesture_recognition\gesture_recognizer.py
echo             # Read frame from webcam >> gesture_recognition\gesture_recognizer.py
echo             ret, frame = cap.read() >> gesture_recognition\gesture_recognizer.py
echo             if not ret: >> gesture_recognition\gesture_recognizer.py
echo                 print("Error: Could not read frame.") >> gesture_recognition\gesture_recognizer.py
echo                 break >> gesture_recognition\gesture_recognizer.py
echo. >> gesture_recognition\gesture_recognizer.py
echo             # Flip the frame horizontally for a more intuitive mirror view >> gesture_recognition\gesture_recognizer.py
echo             frame = cv2.flip(frame, 1) >> gesture_recognition\gesture_recognizer.py
echo. >> gesture_recognition\gesture_recognizer.py
echo             # Detect gesture >> gesture_recognition\gesture_recognizer.py
echo             gesture, confidence, annotated_frame = detect_gesture(frame) >> gesture_recognition\gesture_recognizer.py
echo. >> gesture_recognition\gesture_recognizer.py
echo             # Display instructions >> gesture_recognition\gesture_recognizer.py
echo             cv2.putText(annotated_frame, "Peace Sign (V) = 200ml", (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1) >> gesture_recognition\gesture_recognizer.py
echo             cv2.putText(annotated_frame, "Thumbs Up = 330ml", (10, 80), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1) >> gesture_recognition\gesture_recognizer.py
echo             cv2.putText(annotated_frame, "Press 'Q' to quit", (10, 100), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1) >> gesture_recognition\gesture_recognizer.py
echo. >> gesture_recognition\gesture_recognizer.py
echo             # Track consistent gesture detection >> gesture_recognition\gesture_recognizer.py
echo             if gesture: >> gesture_recognition\gesture_recognizer.py
echo                 if current_gesture == gesture: >> gesture_recognition\gesture_recognizer.py
echo                     gesture_frames += 1 >> gesture_recognition\gesture_recognizer.py
echo                     if gesture_frames >= 10 and not gesture_detected:  # Need 10 consistent frames >> gesture_recognition\gesture_recognizer.py
echo                         gesture_detected = True >> gesture_recognition\gesture_recognizer.py
echo                         # Determine amount based on gesture >> gesture_recognition\gesture_recognizer.py
echo                         amount = 200 if gesture == "peace_sign" else 330 >> gesture_recognition\gesture_recognizer.py
echo. >> gesture_recognition\gesture_recognizer.py
echo                         # Log water directly >> gesture_recognition\gesture_recognizer.py
echo                         success, message = log_water_directly(amount, gesture) >> gesture_recognition\gesture_recognizer.py
echo. >> gesture_recognition\gesture_recognizer.py
echo                         # Display success message >> gesture_recognition\gesture_recognizer.py
echo                         if success: >> gesture_recognition\gesture_recognizer.py
echo                             cv2.putText(annotated_frame, f"Logged {amount}ml of water!", (10, 130), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2) >> gesture_recognition\gesture_recognizer.py
echo                         else: >> gesture_recognition\gesture_recognizer.py
echo                             cv2.putText(annotated_frame, "Error logging water", (10, 130), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2) >> gesture_recognition\gesture_recognizer.py
echo. >> gesture_recognition\gesture_recognizer.py
echo                         # Save the result >> gesture_recognition\gesture_recognizer.py
echo                         result = { >> gesture_recognition\gesture_recognizer.py
echo                             "success": True, >> gesture_recognition\gesture_recognizer.py
echo                             "gesture": gesture, >> gesture_recognition\gesture_recognizer.py
echo                             "confidence": confidence, >> gesture_recognition\gesture_recognizer.py
echo                             "amount": amount, >> gesture_recognition\gesture_recognizer.py
echo                             "timestamp": datetime.now().isoformat() >> gesture_recognition\gesture_recognizer.py
echo                         } >> gesture_recognition\gesture_recognizer.py
echo. >> gesture_recognition\gesture_recognizer.py
echo                         # Save the result to a file >> gesture_recognition\gesture_recognizer.py
echo                         with open("gesture_result.json", "w") as f: >> gesture_recognition\gesture_recognizer.py
echo                             json.dump(result, f) >> gesture_recognition\gesture_recognizer.py
echo. >> gesture_recognition\gesture_recognizer.py
echo                         # Save the annotated image >> gesture_recognition\gesture_recognizer.py
echo                         cv2.imwrite("gesture_image.jpg", annotated_frame) >> gesture_recognition\gesture_recognizer.py
echo. >> gesture_recognition\gesture_recognizer.py
echo                         # Wait for 3 seconds to show the result >> gesture_recognition\gesture_recognizer.py
echo                         start_time = time.time() >> gesture_recognition\gesture_recognizer.py
echo                         while time.time() - start_time < 3: >> gesture_recognition\gesture_recognizer.py
echo                             # Display the result >> gesture_recognition\gesture_recognizer.py
echo                             cv2.putText(annotated_frame, f"Logged {amount}ml of water!", (10, 130), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2) >> gesture_recognition\gesture_recognizer.py
echo                             cv2.putText(annotated_frame, f"Detected {gesture}", (10, 160), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2) >> gesture_recognition\gesture_recognizer.py
echo                             cv2.imshow("Gesture Recognition", annotated_frame) >> gesture_recognition\gesture_recognizer.py
echo                             if cv2.waitKey(1) & 0xFF == ord('q'): >> gesture_recognition\gesture_recognizer.py
echo                                 break >> gesture_recognition\gesture_recognizer.py
echo. >> gesture_recognition\gesture_recognizer.py
echo                         # Exit after logging >> gesture_recognition\gesture_recognizer.py
echo                         break >> gesture_recognition\gesture_recognizer.py
echo                 else: >> gesture_recognition\gesture_recognizer.py
echo                     current_gesture = gesture >> gesture_recognition\gesture_recognizer.py
echo                     gesture_frames = 1 >> gesture_recognition\gesture_recognizer.py
echo                     gesture_start_time = time.time() >> gesture_recognition\gesture_recognizer.py
echo             else: >> gesture_recognition\gesture_recognizer.py
echo                 current_gesture = None >> gesture_recognition\gesture_recognizer.py
echo                 gesture_frames = 0 >> gesture_recognition\gesture_recognizer.py
echo. >> gesture_recognition\gesture_recognizer.py
echo             # Display the frame >> gesture_recognition\gesture_recognizer.py
echo             cv2.imshow("Gesture Recognition", annotated_frame) >> gesture_recognition\gesture_recognizer.py
echo. >> gesture_recognition\gesture_recognizer.py
echo             # Exit on 'q' key >> gesture_recognition\gesture_recognizer.py
echo             if cv2.waitKey(1) & 0xFF == ord('q'): >> gesture_recognition\gesture_recognizer.py
echo                 break >> gesture_recognition\gesture_recognizer.py
echo. >> gesture_recognition\gesture_recognizer.py
echo     except Exception as e: >> gesture_recognition\gesture_recognizer.py
echo         print(f"Error: {str(e)}") >> gesture_recognition\gesture_recognizer.py
echo         result = { >> gesture_recognition\gesture_recognizer.py
echo             "success": False, >> gesture_recognition\gesture_recognizer.py
echo             "error": str(e), >> gesture_recognition\gesture_recognizer.py
echo             "gesture": None, >> gesture_recognition\gesture_recognizer.py
echo             "annotated_image": None >> gesture_recognition\gesture_recognizer.py
echo         } >> gesture_recognition\gesture_recognizer.py
echo         with open("gesture_result.json", "w") as f: >> gesture_recognition\gesture_recognizer.py
echo             json.dump(result, f) >> gesture_recognition\gesture_recognizer.py
echo     finally: >> gesture_recognition\gesture_recognizer.py
echo         # Release resources >> gesture_recognition\gesture_recognizer.py
echo         cap.release() >> gesture_recognition\gesture_recognizer.py
echo         cv2.destroyAllWindows() >> gesture_recognition\gesture_recognizer.py
echo. >> gesture_recognition\gesture_recognizer.py
echo if __name__ == "__main__": >> gesture_recognition\gesture_recognizer.py
echo     main() >> gesture_recognition\gesture_recognizer.py

REM Create a launcher script
echo @echo off > run_gesture_recognition.bat
echo call gesture-env\Scripts\activate.bat >> run_gesture_recognition.bat
echo python gesture_recognition\gesture_recognizer.py >> run_gesture_recognition.bat
echo pause >> run_gesture_recognition.bat

echo Setup complete!
echo To run the gesture recognition, execute run_gesture_recognition.bat

REM Deactivate the virtual environment
call gesture-env\Scripts\deactivate.bat

pause
