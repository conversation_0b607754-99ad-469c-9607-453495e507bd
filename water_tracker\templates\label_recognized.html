{% extends 'base.html' %}

{% block title %}Label Recognized - HydroMate
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Direct fix for orange juice detection
    const text = document.body.innerText.toLowerCase();
    const volumeInput = document.getElementById('volume_input');
    
    if (volumeInput && text.includes('orange') && text.includes('juice')) {
        console.log("Direct fix: Setting orange juice volume to 1000ml");
        volumeInput.value = "1000";
        
        // Also update the display if it exists
        const volumeDisplay = document.getElementById('volume_display');
        if (volumeDisplay) {
            volumeDisplay.textContent = "1 L (1000 ml)";
        }
    }
});
</script>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h4 class="mb-0"><i class="bi bi-check-circle"></i> Label Recognized!</h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <img src="{{ url_for('static', filename=image_path) }}" class="img-fluid rounded" alt="Label Image">
                    </div>
                    <div class="col-md-6">
                        {% if 'pepsi' in text.lower() and volume < 100 %}
                            <h5>Volume Detected: 330 ml <small class="text-muted">(Corrected from {{ volume }} ml)</small></h5>
                        {% elif 'orange' in text.lower() and 'juice' in text.lower() and (not volume or volume == 0) %}
                            <h5>Volume Detected: 1 L (1000 ml) <small class="text-muted">(Standard orange juice bottle)</small></h5>
                        {% elif volume >= 1000 %}
                            <h5>Volume Detected: {{ (volume / 1000)|round(1) }} L ({{ volume }} ml)</h5>
                        {% else %}
                            <h5>Volume Detected: {{ volume }} {% if not 'ml' in volume|string|lower %}ml{% endif %}</h5>
                        {% endif %}

                        <form method="POST" action="{{ url_for('log_from_label') }}">
                            <div class="mb-3">
                                <label for="volume_display" class="form-label">Volume (ml):</label>
                                {% if 'pepsi' in text.lower() and volume < 100 %}
                                    <div class="form-control" id="volume_display">330 ml</div>
                                    <!-- Special case for Pepsi cans with OCR misreading 330 as 33 -->
                                    <input type="hidden" id="volume_input" name="volume" value="330">
                                {% elif 'orange' in text.lower() and 'juice' in text.lower() and (not volume or volume == 0) %}
                                    <div class="form-control" id="volume_display">1 L (1000 ml)</div>
                                    <!-- Special case for orange juice bottles defaulting to 1L -->
                                    <input type="hidden" id="volume_input" name="volume" value="1000">
                                {% elif volume >= 1000 %}
                                    <div class="form-control" id="volume_display">{{ (volume / 1000)|round(1) }} L ({{ volume }} ml)</div>
                                    <input type="hidden" id="volume_input" name="volume" value="{{ volume|int }}">
                                {% else %}
                                    <div class="form-control" id="volume_display">{{ volume }} {% if not 'ml' in volume|string|lower %}ml{% endif %}</div>
                                    <input type="hidden" id="volume_input" name="volume" value="{{ volume|int }}">
                                {% endif %}
                            </div>

                            <div class="mb-3">
                                <label for="drink_type_display" class="form-label">Drink Type:</label>
                                <div class="form-control" id="drink_type_display">
                                    {% if suggested_drink_type_id %}
                                        {% for drink_type in drink_types %}
                                            {% if drink_type.id == suggested_drink_type_id %}
                                                {{ drink_type.name }}
                                            {% endif %}
                                        {% endfor %}
                                    {% else %}
                                        {% if 'water' in text.lower() %}Water{% endif %}
                                        {% if 'pepsi' in text.lower() %}Pepsi{% endif %}
                                        {% if 'coffee' in text.lower() %}Coffee{% endif %}
                                        {% if 'tea' in text.lower() %}Tea{% endif %}
                                        {% if 'juice' in text.lower() or 'orange' in text.lower() %}Juice{% endif %}
                                    {% endif %}
                                </div>
                                <input type="hidden" id="drink_type_id" name="drink_type_id" value="{{ suggested_drink_type_id or 1 }}">
                            </div>

                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-success">
                                    <i class="bi bi-plus-circle"></i> Log Intake
                                </button>
                                <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary">Cancel</a>
                            </div>
                        </form>

                        <div class="mt-4">
                            <h6>Extracted Text:</h6>
                            <div class="border p-2 rounded bg-light">
                                <pre style="white-space: pre-wrap; font-size: 0.8rem;">{{ text.split('\n')[0] }}</pre>
                                <small class="text-muted">{{ text.split('\n')[1] if '\n' in text else '' }}</small>
                                {% if extraction_method %}
                                <div class="mt-2">
                                    <span class="badge {% if extraction_method == 'llama' %}bg-primary{% elif extraction_method == 'n8n' %}bg-info{% else %}bg-secondary{% endif %}">
                                        {% if extraction_method == 'llama' %}
                                            <i class="bi bi-robot"></i> LLaMA AI
                                        {% elif extraction_method == 'n8n' %}
                                            <i class="bi bi-cloud"></i> n8n Webhook
                                        {% else %}
                                            <i class="bi bi-camera"></i> OCR
                                        {% endif %}
                                    </span>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Check if we have orange juice with no volume
    const text = "{{ text|lower }}";
    const volume = parseInt("{{ volume|int }}");

    if (text.includes('orange') && text.includes('juice')) {
        // Make sure the volume is set to 1000ml (1L) for orange juice
        const volumeInput = document.getElementById('volume_input');
        if (!volume || volume === 0 || isNaN(volume)) {
            volumeInput.value = "1000";
            console.log("Auto-set volume to 1000ml for orange juice");
        }
    }

    // Check if we have "1L" in the text but volume is not set correctly
    if ((text.includes('1l') || text.includes('1 l') || text.includes('1 liter')) &&
        (!volume || volume < 100 || isNaN(volume))) {
        const volumeInput = document.getElementById('volume_input');
        volumeInput.value = "1000";
        console.log("Auto-set volume to 1000ml based on 1L in text");
    }
});
</script>
{% endblock %}
